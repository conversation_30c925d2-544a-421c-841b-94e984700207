# GPU優化頭部檢測系統 - WebSocket版本修改說明

## 修改概述

已成功將 `gpu_optimized_head_detection.py` 從 RTSP 視頻來源改為 WebSocket 視頻來源，參考了 `websocket_viewer.html` 的實現方式。

## 主要修改內容

### 1. 導入庫修改
```python
# 新增導入
import websocket
import base64
from io import BytesIO
from PIL import Image
```

### 2. 創建新的 WebSocket 處理器類
- 將原來的 `GPUOptimizedRTSPProcessor` 改為 `GPUOptimizedWebSocketProcessor`
- 修改初始化參數：`rtsp_url` → `websocket_url`
- 移除硬體解碼相關參數（WebSocket 不需要）

### 3. WebSocket 連接實現
```python
def connect_websocket(self):
    """連接WebSocket流"""
    # 實現 WebSocket 連接邏輯
    # 處理 base64 編碼的 JPEG 圖像
    # 自動重連機制
```

### 4. 消息處理機制
- 接收 `data:image/jpeg;base64,` 格式的消息
- 解碼 base64 數據為圖像
- 轉換為 OpenCV 格式的幀
- 使用隊列機制處理幀數據

### 5. 幀獲取方法
```python
def get_frame(self):
    """從WebSocket獲取幀"""
    # 從隊列中獲取最新幀
```

### 6. 流處理方法修改
- 修改 `process_stream` 方法以使用 WebSocket
- 保持原有的 ROI 檢測和 YOLO 功能
- 保持所有鼠標交互和鍵盤控制功能

### 7. 命令行參數修改
```bash
# 原來
--rtsp-url RTSP_URL

# 修改後
--websocket-url WEBSOCKET_URL  # 默認: ws://*************:8004
```

## 保持不變的功能

### ✅ ROI 區域功能
- 多區域 ROI 繪製和編輯
- 人頭檢測區域（紅色）
- 公車檢測區域（藍色）
- ROI 區域保存和載入

### ✅ YOLO 檢測功能
- GPU 優化的 YOLOv5 檢測
- 批次處理
- 多區域檢測統計
- 檢測結果繪製

### ✅ 交互控制
- 鼠標繪製 ROI 區域
- 鍵盤快捷鍵操作
- 編輯模式
- 顯示切換

### ✅ 視頻處理
- 硬體編碼支持
- 視頻保存功能
- 性能監控
- 統計數據

## 使用方法

### 基本運行
```bash
python gpu_optimized_head_detection.py --websocket-url ws://*************:8004
```

### 完整參數示例
```bash
python gpu_optimized_head_detection.py \
    --websocket-url ws://*************:8004 \
    --model-type person \
    --conf-threshold 0.15 \
    --device auto \
    --save-video \
    --output-path websocket_detection.mp4 \
    --detection-interval 5 \
    --video-quality medium
```

## WebSocket 服務器要求

WebSocket 服務器需要發送以下格式的消息：
```
data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=
```

## 依賴庫

確保安裝以下庫：
```bash
pip install websocket-client pillow
```

## 測試連接

可以使用以下代碼測試 WebSocket 連接：
```python
import websocket

def on_message(ws, message):
    if message.startswith('data:image/jpeg;base64,'):
        print('✅ 收到有效的圖像數據')

def on_open(ws):
    print('✅ WebSocket連接成功')

ws = websocket.WebSocketApp('ws://*************:8004',
                          on_message=on_message,
                          on_open=on_open)
ws.run_forever()
```

## 注意事項

1. **WebSocket 服務器必須運行**：確保 `ws://*************:8004` 有 WebSocket 服務器在運行
2. **圖像格式**：服務器必須發送 base64 編碼的 JPEG 圖像
3. **網絡延遲**：WebSocket 可能比 RTSP 有更低的延遲
4. **資源使用**：WebSocket 版本使用隊列機制，記憶體使用可能略有不同

## 故障排除

### 連接失敗
- 檢查 WebSocket 服務器是否運行
- 確認 IP 地址和端口正確
- 檢查防火牆設置

### 沒有圖像顯示
- 確認 WebSocket 服務器發送正確格式的數據
- 檢查 base64 解碼是否成功
- 查看控制台錯誤信息

### 性能問題
- 調整 `--detection-interval` 參數
- 使用 GPU 加速（`--device cuda`）
- 降低視頻品質設定
