#!/usr/bin/env python3
"""
PyTorch 2.7+ 終極修復方案
針對最新版本 PyTorch 的安全性限制問題
"""

import torch
import os
import warnings
import logging

logger = logging.getLogger(__name__)

def ultimate_pytorch_fix():
    """終極 PyTorch 修復方案"""
    
    print("🚨 執行 PyTorch 2.7+ 終極修復...")
    
    # 1. 全局設置環境變量
    os.environ.update({
        'TORCH_SERIALIZATION_SAFE_GLOBALS': '1',
        'TORCH_HOME': os.path.expanduser('~/.cache/torch'),
        'TORCH_HUB_CACHE': os.path.expanduser('~/.cache/torch/hub'),
        'PYTORCH_DISABLE_SAFE_GLOBALS': '1',  # 新增
    })
    
    # 2. 添加所有可能需要的安全全局變量
    try:
        if hasattr(torch.serialization, 'add_safe_globals'):
            comprehensive_safe_globals = [
                # numpy 相關
                'numpy.core.multiarray._reconstruct',
                'numpy.ndarray',
                'numpy.dtype',
                'numpy.core.multiarray.scalar',
                'numpy.core._multiarray_umath._reconstruct',
                'numpy.ma.core._mareconstruct',
                'numpy.random.__random_state_ctor',
                
                # collections 相關
                'collections.OrderedDict',
                'collections.defaultdict',
                'collections.deque',
                'collections.Counter',
                
                # torch 相關
                'torch._utils._rebuild_tensor_v2',
                'torch._utils._rebuild_parameter',
                'torch._utils._rebuild_qtensor',
                'torch.nn.parameter.Parameter',
                'torch.Tensor',
                'torch.Size',
                'torch.dtype',
                'torch.device',
                
                # 其他常見類型
                '__builtin__.slice',
                'builtins.slice',
                'builtins.range',
                'builtins.tuple',
                'builtins.list',
                'builtins.dict',
                'builtins.set',
                'builtins.frozenset',
            ]
            
            torch.serialization.add_safe_globals(comprehensive_safe_globals)
            print(f"✅ 已添加 {len(comprehensive_safe_globals)} 個安全全局變量")
    except Exception as e:
        print(f"⚠️ 添加安全全局變量失敗: {e}")
    
    # 3. 徹底修補 torch.load
    try:
        if not hasattr(torch, '_original_load_backup'):
            torch._original_load_backup = torch.load
        
        def ultimate_patched_load(*args, **kwargs):
            """終極修補版 torch.load"""
            # 強制設置 weights_only=False
            kwargs['weights_only'] = False
            
            # 使用最寬鬆的安全設置
            if hasattr(torch.serialization, 'safe_globals'):
                with torch.serialization.safe_globals(comprehensive_safe_globals):
                    return torch._original_load_backup(*args, **kwargs)
            else:
                return torch._original_load_backup(*args, **kwargs)
        
        torch.load = ultimate_patched_load
        print("✅ 已徹底修補 torch.load")
        
    except Exception as e:
        print(f"⚠️ 修補 torch.load 失敗: {e}")
    
    # 4. 修補 torch.hub.load
    try:
        if not hasattr(torch.hub, '_original_load_backup'):
            torch.hub._original_load_backup = torch.hub.load
        
        def ultimate_patched_hub_load(*args, **kwargs):
            """終極修補版 torch.hub.load"""
            # 只設置 torch.hub.load 支持的參數
            # 移除 weights_only（這是 torch.load 的參數）
            hub_kwargs = {
                'trust_repo': kwargs.pop('trust_repo', True),
                'verbose': kwargs.pop('verbose', False),
                'force_reload': kwargs.pop('force_reload', True),
                'skip_validation': kwargs.pop('skip_validation', True),
            }
            
            # 合併其他參數
            kwargs.update(hub_kwargs)
            
            # 禁用所有警告
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore")
                
                # 使用安全上下文
                if hasattr(torch.serialization, 'safe_globals'):
                    with torch.serialization.safe_globals(comprehensive_safe_globals):
                        return torch.hub._original_load_backup(*args, **kwargs)
                else:
                    return torch.hub._original_load_backup(*args, **kwargs)
        
        torch.hub.load = ultimate_patched_hub_load
        print("✅ 已徹底修補 torch.hub.load")
        
    except Exception as e:
        print(f"⚠️ 修補 torch.hub.load 失敗: {e}")
    
    # 5. 禁用所有相關警告
    warning_filters = [
        ("ignore", FutureWarning),
        ("ignore", UserWarning),
        ("ignore", DeprecationWarning),
        ("ignore", RuntimeWarning),
    ]
    
    for action, category in warning_filters:
        warnings.filterwarnings(action, category=category)
        warnings.filterwarnings(action, category=category, module="torch")
        warnings.filterwarnings(action, category=category, module="torchvision")
        warnings.filterwarnings(action, category=category, module="ultralytics")
    
    # 特定警告消息
    specific_warnings = [
        ".*weights_only.*",
        ".*safe_globals.*",
        ".*WeightsUnpickler.*",
        ".*arbitrary code execution.*",
        ".*trusted source.*",
    ]
    
    for pattern in specific_warnings:
        warnings.filterwarnings("ignore", message=pattern)
    
    print("✅ 已禁用所有警告")
    
    # 6. 清理緩存（可選）
    try:
        cache_dir = torch.hub.get_dir()
        print(f"📁 Torch Hub 緩存目錄: {cache_dir}")
        
        # 檢查是否需要清理緩存
        ultralytics_cache = os.path.join(cache_dir, "ultralytics_yolov5_master")
        if os.path.exists(ultralytics_cache):
            print(f"🗑️ 發現舊緩存，建議清理: {ultralytics_cache}")
            # 不自動刪除，讓用戶決定
            
    except Exception as e:
        print(f"⚠️ 檢查緩存失敗: {e}")
    
    print("🎯 終極修復完成！")
    return True

def clear_torch_cache():
    """清理 Torch Hub 緩存"""
    try:
        import shutil
        cache_dir = torch.hub.get_dir()
        
        if os.path.exists(cache_dir):
            print(f"🗑️ 清理緩存目錄: {cache_dir}")
            shutil.rmtree(cache_dir)
            print("✅ 緩存清理完成")
            return True
    except Exception as e:
        print(f"❌ 清理緩存失敗: {e}")
        return False

def test_model_loading():
    """測試模型加載"""
    try:
        print("🧪 測試模型加載...")
        
        # 嘗試加載最小的模型
        model = torch.hub.load('ultralytics/yolov5', 'yolov5n', pretrained=True)
        print("✅ 模型加載成功！")
        
        # 簡單測試
        dummy_input = torch.randn(1, 3, 640, 640)
        with torch.no_grad():
            results = model(dummy_input)
        print("✅ 模型推理成功！")
        
        # 清理
        del model, dummy_input, results
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        return True
        
    except Exception as e:
        print(f"❌ 模型加載測試失敗: {e}")
        return False

if __name__ == "__main__":
    print(f"PyTorch 版本: {torch.__version__}")
    print(f"CUDA 可用: {torch.cuda.is_available()}")
    
    # 執行終極修復
    ultimate_pytorch_fix()
    
    # 測試模型加載
    if not test_model_loading():
        print("\n🔄 模型加載失敗，嘗試清理緩存...")
        if clear_torch_cache():
            print("🔄 重新測試模型加載...")
            test_model_loading()
    
    print("\n🚀 修復完成！現在可以運行主程序了。")