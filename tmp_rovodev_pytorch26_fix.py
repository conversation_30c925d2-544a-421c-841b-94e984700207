#!/usr/bin/env python3
"""
PyTorch 2.6 安全性修復版本
解決 torch.hub.load 在 PyTorch 2.6 中的安全性限制問題
"""

import torch
import logging
import warnings
import os

logger = logging.getLogger(__name__)

def setup_torch_safe_globals():
    """設置 PyTorch 安全全局變量，解決 numpy 相關錯誤"""
    try:
        # 添加 numpy 相關的安全全局變量
        safe_globals = [
            'numpy.core.multiarray._reconstruct',
            'numpy.ndarray',
            'numpy.dtype',
            'numpy.core.multiarray.scalar',
            'collections.OrderedDict',
            'torch._utils._rebuild_tensor_v2',
            'torch._utils._rebuild_parameter',
            'torch._utils._rebuild_qtensor',
        ]
        
        # PyTorch 2.6+ 需要明確添加安全全局變量
        if hasattr(torch.serialization, 'add_safe_globals'):
            torch.serialization.add_safe_globals(safe_globals)
            logger.info("✅ 已添加 PyTorch 安全全局變量")
        
    except Exception as e:
        logger.warning(f"⚠️ 設置安全全局變量失敗: {e}")

def patch_torch_load():
    """修補 torch.load 函數以支持 PyTorch 2.6"""
    try:
        # 保存原始的 torch.load
        if not hasattr(torch, '_original_load'):
            torch._original_load = torch.load
        
        def patched_load(*args, **kwargs):
            """修補後的 torch.load 函數"""
            # 檢查 PyTorch 版本
            pytorch_version = torch.__version__
            major, minor = map(int, pytorch_version.split('.')[:2])
            
            if major > 2 or (major == 2 and minor >= 6):
                # PyTorch 2.6+ 默認設置 weights_only=False
                if 'weights_only' not in kwargs:
                    kwargs['weights_only'] = False
                    
                # 使用安全全局變量上下文
                if hasattr(torch.serialization, 'safe_globals'):
                    safe_globals = [
                        'numpy.core.multiarray._reconstruct',
                        'numpy.ndarray',
                        'numpy.dtype',
                        'numpy.core.multiarray.scalar',
                        'collections.OrderedDict',
                    ]
                    with torch.serialization.safe_globals(safe_globals):
                        return torch._original_load(*args, **kwargs)
            
            return torch._original_load(*args, **kwargs)
        
        # 替換 torch.load
        torch.load = patched_load
        logger.info("✅ 已修補 torch.load 函數")
        
    except Exception as e:
        logger.warning(f"⚠️ 修補 torch.load 失敗: {e}")

def safe_torch_hub_load(repo_or_dir, model, *args, **kwargs):
    """
    安全的 torch.hub.load 包裝函數，兼容 PyTorch 2.6+
    """
    try:
        # PyTorch 2.6+ 需要明確設置 weights_only=False 來加載完整模型
        if hasattr(torch, 'hub') and hasattr(torch.hub, 'load'):
            # 檢查 PyTorch 版本
            pytorch_version = torch.__version__
            major, minor = map(int, pytorch_version.split('.')[:2])
            
            if major > 2 or (major == 2 and minor >= 6):
                logger.info(f"🔧 檢測到 PyTorch {pytorch_version}，使用安全模式加載模型")
                
                # 對於 PyTorch 2.6+，需要明確設置安全參數
                if 'weights_only' not in kwargs:
                    kwargs['weights_only'] = False
                    
                # 添加信任設置
                if 'trust_repo' not in kwargs:
                    kwargs['trust_repo'] = True
                    
                # 禁用安全檢查警告
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=FutureWarning)
                    warnings.filterwarnings("ignore", category=UserWarning)
                    return torch.hub.load(repo_or_dir, model, *args, **kwargs)
            else:
                # PyTorch < 2.6，使用原始方法
                logger.info(f"🔧 檢測到 PyTorch {pytorch_version}，使用標準模式加載模型")
                return torch.hub.load(repo_or_dir, model, *args, **kwargs)
                
    except Exception as e:
        logger.error(f"❌ 模型加載失敗: {e}")
        
        # 嘗試備用方法
        logger.info("🔄 嘗試備用加載方法...")
        try:
            # 方法1: 清理緩存並重新加載
            cache_dir = torch.hub.get_dir()
            logger.info(f"清理 torch hub 緩存: {cache_dir}")
            
            # 方法2: 強制重新加載並設置所有安全參數
            kwargs.update({
                'force_reload': True,
                'verbose': False,
                'weights_only': False,
                'trust_repo': True,
                'skip_validation': True
            })
            
            # 臨時設置環境變量
            old_env = os.environ.get('TORCH_SERIALIZATION_SAFE_GLOBALS')
            os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = '1'
            
            try:
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore")
                    result = torch.hub.load(repo_or_dir, model, *args, **kwargs)
                    return result
            finally:
                # 恢復環境變量
                if old_env is not None:
                    os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = old_env
                else:
                    os.environ.pop('TORCH_SERIALIZATION_SAFE_GLOBALS', None)
                
        except Exception as e2:
            logger.error(f"❌ 備用方法失敗: {e2}")
            
            # 方法3: 嘗試本地模型文件
            logger.info("🔄 嘗試使用本地模型文件...")
            try:
                if model in ['yolov5s', 'yolov5m', 'yolov5l', 'yolov5x', 'yolov5n']:
                    local_model_path = f"{model}.pt"
                    if os.path.exists(local_model_path):
                        logger.info(f"✅ 找到本地模型文件: {local_model_path}")
                        # 使用本地文件加載
                        model_instance = torch.hub.load('ultralytics/yolov5', 'custom', 
                                                      path=local_model_path, 
                                                      weights_only=False, 
                                                      trust_repo=True,
                                                      force_reload=True)
                        return model_instance
                
                # 嘗試下載模型到本地
                logger.info("🔄 嘗試下載模型到本地...")
                download_url = f"https://github.com/ultralytics/yolov5/releases/download/v7.0/{model}.pt"
                local_path = f"{model}.pt"
                
                import urllib.request
                urllib.request.urlretrieve(download_url, local_path)
                logger.info(f"✅ 模型下載完成: {local_path}")
                
                model_instance = torch.hub.load('ultralytics/yolov5', 'custom', 
                                              path=local_path, 
                                              weights_only=False, 
                                              trust_repo=True)
                return model_instance
                        
            except Exception as e3:
                logger.error(f"❌ 本地模型方法也失敗: {e3}")
            
            # 最後的備用方案：提供詳細的錯誤信息和解決建議
            logger.error("❌ 所有模型加載方法都失敗")
            logger.error("💡 建議解決方案:")
            logger.error("   1. 檢查網絡連接")
            logger.error("   2. 手動下載模型文件到當前目錄")
            logger.error("   3. 清理 torch hub 緩存")
            logger.error("   4. 考慮降級到 PyTorch 2.5.x")
            raise e

def check_pytorch_compatibility():
    """檢查 PyTorch 版本兼容性"""
    info = {
        'version': torch.__version__,
        'cuda_available': torch.cuda.is_available(),
        'hub_available': hasattr(torch, 'hub'),
        'needs_security_fix': False,
        'recommendations': []
    }
    
    try:
        major, minor = map(int, torch.__version__.split('.')[:2])
        
        if major > 2 or (major == 2 and minor >= 6):
            info['needs_security_fix'] = True
            info['recommendations'].append("使用 safe_torch_hub_load 函數替代 torch.hub.load")
            info['recommendations'].append("確保設置 weights_only=False 和 trust_repo=True")
            
        if major < 1 or (major == 1 and minor < 9):
            info['recommendations'].append("建議升級到 PyTorch 1.9+ 以獲得更好的性能")
            
    except Exception as e:
        logger.warning(f"無法解析 PyTorch 版本: {e}")
        
    return info

def apply_pytorch26_fixes():
    """應用 PyTorch 2.6 相關修復"""
    logger.info("🔧 應用 PyTorch 2.6 兼容性修復...")
    
    # 檢查版本
    compat_info = check_pytorch_compatibility()
    logger.info(f"PyTorch 版本: {compat_info['version']}")
    logger.info(f"CUDA 可用: {compat_info['cuda_available']}")
    logger.info(f"需要安全修復: {compat_info['needs_security_fix']}")
    
    if compat_info['recommendations']:
        logger.info("建議:")
        for rec in compat_info['recommendations']:
            logger.info(f"  - {rec}")
    
    # 設置環境變量
    if 'TORCH_HOME' not in os.environ:
        os.environ['TORCH_HOME'] = os.path.expanduser('~/.cache/torch')
        logger.info(f"設置 TORCH_HOME: {os.environ['TORCH_HOME']}")
    
    # 應用 PyTorch 2.6 特定修復
    major, minor = map(int, compat_info['version'].split('.')[:2])
    if major > 2 or (major == 2 and minor >= 6):
        logger.info("🔧 應用 PyTorch 2.6+ 特定修復...")
        
        # 設置安全全局變量
        setup_torch_safe_globals()
        
        # 修補 torch.load
        patch_torch_load()
        
        # 設置環境變量以禁用嚴格模式
        os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = '1'
        
        logger.info("✅ PyTorch 2.6+ 修復完成")
    
    # 禁用不必要的警告
    warnings.filterwarnings("ignore", category=FutureWarning, module="torch")
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")
    warnings.filterwarnings("ignore", category=UserWarning, module="torchvision")
    
    return compat_info

if __name__ == "__main__":
    # 測試兼容性
    info = apply_pytorch26_fixes()
    print("PyTorch 兼容性檢查完成")
    print(f"版本: {info['version']}")
    print(f"需要修復: {info['needs_security_fix']}")