#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全修復版本的GPU優化頭部檢測系統
解決了所有編碼問題、重複類定義和參數不匹配問題
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detect_hardware_capabilities():
    """檢測系統硬體能力"""
    capabilities = {
        'cuda_available': torch.cuda.is_available(),
        'gpu_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
        'gpu_memory': 0,
        'gpu_name': ''
    }
    
    if capabilities['cuda_available']:
        try:
            capabilities['gpu_name'] = torch.cuda.get_device_name(0)
            capabilities['gpu_memory'] = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"檢測到GPU: {capabilities['gpu_name']} ({capabilities['gpu_memory']:.1f}GB)")
        except Exception as e:
            logger.warning(f"GPU信息檢測失敗: {e}")
    
    return capabilities

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.15, device='auto', batch_size=1):
        """GPU優化的頭部檢測器"""
        self.conf_threshold = conf_threshold
        self.requested_device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # 自動偵測並設置最佳設備
        self.device = self._auto_detect_device()
        
        # 設置GPU優化參數
        self._setup_optimization()
        
        # 加載模型
        self._load_model()
        
        logger.info(f"檢測器初始化完成: {self.device}")
    
    def _auto_detect_device(self):
        """自動偵測設備"""
        if self.requested_device == 'cpu':
            return 'cpu'
        elif self.requested_device == 'cuda':
            if torch.cuda.is_available():
                return 'cuda'
            else:
                logger.warning("CUDA不可用，切換到CPU")
                return 'cpu'
        else:  # auto
            return 'cuda' if torch.cuda.is_available() else 'cpu'
    
    def _setup_optimization(self):
        """設置優化參數"""
        if self.device == 'cuda':
            torch.backends.cudnn.benchmark = True
            torch.cuda.empty_cache()
            logger.info("GPU優化已啟用")
        else:
            torch.set_num_threads(4)
            logger.info("CPU優化已啟用")
    
    def _load_model(self):
        """加載模型"""
        try:
            logger.info("加載YOLOv5模型...")
            self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            
            if self.device == 'cuda':
                self.model.half()
                logger.info("啟用半精度推理")
            
            logger.info("模型加載完成")
            
        except Exception as e:
            logger.error(f"模型加載失敗: {e}")
            raise

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='output.mp4', 
                 detection_interval=1, video_quality='medium', target_fps=None, 
                 target_resolution=None, use_hardware_encoding=True, use_hardware_decoding=True):
        """
        GPU優化的RTSP處理器
        
        Args:
            rtsp_url: RTSP流地址
            detector: 檢測器實例
            save_video: 是否保存視頻
            output_path: 輸出路徑
            detection_interval: 檢測間隔
            video_quality: 視頻品質
            target_fps: 目標FPS
            target_resolution: 目標解析度
            use_hardware_encoding: 使用硬體編碼
            use_hardware_decoding: 使用硬體解碼
        """
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.detection_interval = detection_interval
        self.video_quality = video_quality
        self.target_fps = target_fps
        self.target_resolution = target_resolution
        self.use_hardware_encoding = use_hardware_encoding
        self.use_hardware_decoding = use_hardware_decoding
        
        # 視頻相關
        self.cap = None
        self.out = None
        
        # 性能監控
        self.frame_times = []
        self.detection_frame_count = 0
        
        logger.info("RTSP處理器初始化完成")
        logger.info(f"硬體編碼: {'啟用' if use_hardware_encoding else '停用'}")
        logger.info(f"硬體解碼: {'啟用' if use_hardware_decoding else '停用'}")
    
    def connect_stream(self):
        """連接RTSP流"""
        try:
            logger.info(f"連接RTSP流: {self.rtsp_url}")
            self.cap = cv2.VideoCapture(self.rtsp_url)
            
            if not self.cap.isOpened():
                raise Exception("無法連接到RTSP流")
            
            # 設置緩衝區
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流連接成功: {width}x{height} @ {fps}fps")
            
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"視頻將保存到: {self.output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"連接RTSP流失敗: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """處理視頻流"""
        if not self.connect_stream():
            return
        
        if display:
            cv2.namedWindow("GPU_Head_Detection", cv2.WINDOW_NORMAL)
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("無法讀取幀")
                    break
                
                frame_count += 1
                
                # 檢測間隔控制
                should_detect = (frame_count % self.detection_interval == 1)
                
                if should_detect:
                    # 執行檢測
                    detection_start = time.time()
                    results = self.detector.model(frame)
                    detection_time = time.time() - detection_start
                    self.detection_frame_count += 1
                    
                    # 處理檢測結果
                    annotated_frame = self._process_results(frame, results)
                else:
                    annotated_frame = frame.copy()
                    detection_time = 0
                
                # 顯示FPS信息
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                cv2.putText(annotated_frame, f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms', 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                if display:
                    cv2.imshow("GPU_Head_Detection", annotated_frame)
                    
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                
                if self.save_video and self.out:
                    self.out.write(annotated_frame)
                
                if max_frames and frame_count >= max_frames:
                    break
                
                if frame_count % 100 == 0:
                    logger.info(f"已處理 {frame_count} 幀, 平均FPS: {fps:.1f}")
                    
        except KeyboardInterrupt:
            logger.info("用戶中斷")
        except Exception as e:
            logger.error(f"處理過程中出錯: {e}")
        finally:
            self.cleanup()
    
    def _process_results(self, frame, results):
        """處理檢測結果"""
        try:
            detections = results.pandas().xyxy[0]
            person_detections = detections[detections['class'] == 0]  # person class
            
            count = 0
            for _, detection in person_detections.iterrows():
                if detection['confidence'] > self.detector.conf_threshold:
                    x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                    
                    # 繪製檢測框
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 標籤
                    label = f'Person {count+1}: {detection["confidence"]:.2f}'
                    cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    count += 1
            
            # 顯示總計數
            cv2.putText(frame, f'Total: {count}', (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            return frame
            
        except Exception as e:
            logger.error(f"處理檢測結果時出錯: {e}")
            return frame
    
    def cleanup(self):
        """清理資源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("資源清理完成")

def main():
    parser = argparse.ArgumentParser(description='修復版GPU優化頭部檢測系統')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1280x800',
                       help='RTSP流地址')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['cpu', 'cuda', 'auto'], help='運行設備')
    parser.add_argument('--save-video', action='store_true', help='保存視頻')
    parser.add_argument('--output-path', type=str, default='output.mp4', help='輸出路徑')
    parser.add_argument('--no-display', action='store_true', help='不顯示視頻')
    parser.add_argument('--max-frames', type=int, default=None, help='最大處理幀數')
    parser.add_argument('--detection-interval', type=int, default=10, help='檢測間隔')
    parser.add_argument('--use-hardware-encoding', action='store_true', default=True, help='使用硬體編碼')
    parser.add_argument('--use-hardware-decoding', action='store_true', default=True, help='使用硬體解碼')
    
    args = parser.parse_args()
    
    try:
        # 檢測硬體能力
        capabilities = detect_hardware_capabilities()
        logger.info(f"系統硬體能力: {capabilities}")
        
        # 創建檢測器
        detector = GPUOptimizedHeadDetector(
            model_type='person',
            conf_threshold=0.25,
            device=args.device,
            batch_size=1
        )
        
        # 創建處理器
        processor = GPUOptimizedRTSPProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path,
            detection_interval=args.detection_interval,
            use_hardware_encoding=args.use_hardware_encoding,
            use_hardware_decoding=args.use_hardware_decoding
        )
        
        # 開始處理
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序運行出錯: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())