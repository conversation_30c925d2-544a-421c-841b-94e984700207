<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket視頻觀看器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .connection-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .input-group label {
            min-width: 120px;
            font-weight: bold;
        }
        
        .input-group input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .video-frame {
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #000;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .quick-connect {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .quick-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .quick-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 WebSocket視頻觀看器</h1>
            <p>用於觀看RTSP推流系統的WebSocket視頻輸出</p>
        </div>
        
        <div class="connection-panel">
            <h3>連接設定</h3>
            
            <div class="input-group">
                <label for="serverIp">服務器IP:</label>
                <input type="text" id="serverIp" value="*************" placeholder="輸入服務器IP地址">
            </div>
            
            <div class="input-group">
                <label for="wsPort">WebSocket端口:</label>
                <input type="number" id="wsPort" value="7040" placeholder="輸入WebSocket端口">
            </div>
            
            <div class="input-group">
                <label for="streamId">串流ID:</label>
                <input type="text" id="streamId" value="cam7040" placeholder="輸入串流ID (僅顯示用)">
            </div>
            
            <div class="input-group">
                <button id="connectBtn" class="btn btn-primary">連接</button>
                <button id="disconnectBtn" class="btn btn-danger" disabled>斷開</button>
            </div>
            
            <div class="quick-connect">
                <span>快速連接:</span>
                <button class="quick-btn" onclick="quickConnect('*************', 7040, 'cam7040')">cam7040:7040</button>
                <button class="quick-btn" onclick="quickConnect('*************', 7050, 'cam7050')">cam7050:7050</button>
                <button class="quick-btn" onclick="quickConnect('*************', 7060, 'cam7060')">cam7060:7060</button>
            </div>
            
            <div id="status" class="status disconnected">
                ❌ 未連接
            </div>
        </div>
        
        <div class="video-container">
            <img id="videoFrame" class="video-frame" alt="等待視頻..." style="width: 640px; height: 480px; background: #000;">
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="fpsValue">0</div>
                <div class="stat-label">FPS</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="framesValue">0</div>
                <div class="stat-label">總幀數</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="dataValue">0 KB</div>
                <div class="stat-label">數據量</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="latencyValue">0 ms</div>
                <div class="stat-label">延遲</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let frameCount = 0;
        let totalBytes = 0;
        let lastFrameTime = 0;
        let fpsCounter = 0;
        let fpsStartTime = Date.now();
        
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const status = document.getElementById('status');
        const videoFrame = document.getElementById('videoFrame');
        const serverIpInput = document.getElementById('serverIp');
        const wsPortInput = document.getElementById('wsPort');
        const streamIdInput = document.getElementById('streamId');
        
        // 統計元素
        const fpsValue = document.getElementById('fpsValue');
        const framesValue = document.getElementById('framesValue');
        const dataValue = document.getElementById('dataValue');
        const latencyValue = document.getElementById('latencyValue');
        
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        
        function quickConnect(ip, port, streamId) {
            serverIpInput.value = ip;
            wsPortInput.value = port;
            streamIdInput.value = streamId;
            connect();
        }
        
        function connect() {
            const serverIp = serverIpInput.value.trim();
            const wsPort = wsPortInput.value.trim();
            const streamId = streamIdInput.value.trim();
            
            if (!serverIp || !wsPort) {
                alert('請輸入服務器IP和WebSocket端口');
                return;
            }
            
            const wsUrl = `ws://${serverIp}:${wsPort}`;
            
            updateStatus('connecting', `🔄 正在連接 ${wsUrl}...`);
            connectBtn.disabled = true;
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    updateStatus('connected', `✅ 已連接到 ${streamId} (${wsUrl})`);
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    resetStats();
                };
                
                ws.onmessage = function(event) {
                    handleVideoFrame(event.data);
                };
                
                ws.onclose = function(event) {
                    updateStatus('disconnected', `❌ 連接已關閉 (代碼: ${event.code})`);
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                };
                
                ws.onerror = function(error) {
                    updateStatus('disconnected', `❌ 連接錯誤: ${error.message || '未知錯誤'}`);
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                };
                
            } catch (error) {
                updateStatus('disconnected', `❌ 連接失敗: ${error.message}`);
                connectBtn.disabled = false;
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus('disconnected', '❌ 已斷開連接');
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
        }
        
        function updateStatus(type, message) {
            status.className = `status ${type}`;
            status.textContent = message;
        }
        
        function handleVideoFrame(data) {
            if (data.startsWith('data:image/jpeg;base64,')) {
                videoFrame.src = data;
                
                // 更新統計
                frameCount++;
                totalBytes += data.length;
                fpsCounter++;
                
                const now = Date.now();
                if (lastFrameTime > 0) {
                    const latency = now - lastFrameTime;
                    latencyValue.textContent = `${latency} ms`;
                }
                lastFrameTime = now;
                
                // 計算FPS (每秒更新一次)
                if (now - fpsStartTime >= 1000) {
                    const fps = fpsCounter / ((now - fpsStartTime) / 1000);
                    fpsValue.textContent = fps.toFixed(1);
                    fpsCounter = 0;
                    fpsStartTime = now;
                }
                
                // 更新其他統計
                framesValue.textContent = frameCount;
                dataValue.textContent = formatBytes(totalBytes);
            }
        }
        
        function resetStats() {
            frameCount = 0;
            totalBytes = 0;
            fpsCounter = 0;
            fpsStartTime = Date.now();
            lastFrameTime = 0;
            
            fpsValue.textContent = '0';
            framesValue.textContent = '0';
            dataValue.textContent = '0 KB';
            latencyValue.textContent = '0 ms';
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 KB';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }
        
        // 頁面載入時的提示
        window.addEventListener('load', function() {
            console.log('WebSocket視頻觀看器已載入');
            console.log('使用方法:');
            console.log('1. 輸入服務器IP和WebSocket端口');
            console.log('2. 點擊連接按鈕');
            console.log('3. 或使用快速連接按鈕');
        });
    </script>
</body>
</html>