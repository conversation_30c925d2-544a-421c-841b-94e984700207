# 窗口命名統一修復指南

## 問題描述

ROI選取區視窗與主視窗分成了2個，且使用了中文命名，需要統一用英文ASCII命名。

## 修復內容

### 已修復的文件和窗口名稱

#### 1. `gpu_optimized_head_detection.py`
- **修復前**: `"GPU優化頭部檢測 (WebSocket)"`
- **修復後**: `"GPU_Head_Detection_WebSocket"`
- **位置**: 第359行，`GPUOptimizedWebSocketProcessor.__init__` 方法

#### 2. `quick_test.py`
- **修復前**: `'头部检测测试'`
- **修復後**: `'Head_Detection_Test'`
- **修復前**: `'测试图像'`, `'检测结果'`
- **修復後**: `'Test_Image'`, `'Detection_Result'`

#### 3. `demo_new_features.py`
- **修復前**: `'New Features Demo'`
- **修復後**: `'New_Features_Demo'`
- **位置**: 第138和175行

#### 4. `tmp_rovodev_complete_fix.py`
- **修復前**: `"GPU頭部檢測"`
- **修復後**: `"GPU_Head_Detection"`

## 窗口命名規範

### 統一命名格式
所有窗口名稱現在使用以下格式：
- **只使用英文ASCII字符**
- **使用下劃線 `_` 分隔單詞**
- **使用駝峰式命名的變體**

### 命名對照表

| 功能 | 修復前（中文） | 修復後（英文ASCII） |
|------|---------------|-------------------|
| 主檢測窗口 | GPU優化頭部檢測 (WebSocket) | GPU_Head_Detection_WebSocket |
| 頭部檢測測試 | 头部检测测试 | Head_Detection_Test |
| 測試圖像 | 测试图像 | Test_Image |
| 檢測結果 | 检测结果 | Detection_Result |
| 新功能演示 | New Features Demo | New_Features_Demo |
| 新功能測試 | 新功能测试 | New_Features_Test |
| GPU頭部檢測 | GPU頭部檢測 | GPU_Head_Detection |

## 修復效果

### 解決的問題
1. **窗口分離問題**: 統一命名避免了窗口名稱衝突
2. **編碼問題**: 英文ASCII命名避免了中文編碼問題
3. **跨平台兼容性**: 英文命名在所有操作系統上都能正常顯示
4. **代碼可讀性**: 統一的命名規範提高了代碼維護性

### 技術優勢
- **避免編碼衝突**: 不同系統的中文編碼可能不同
- **提高穩定性**: ASCII字符在所有環境下都能正確顯示
- **便於調試**: 英文窗口名稱便於開發者識別和調試
- **國際化友好**: 便於代碼的國際化和本地化

## ROI選取功能

### 窗口統一後的ROI操作
現在所有ROI相關的操作都會在統一命名的窗口中進行：

```python
# ROI選取窗口
cv2.namedWindow('ROI_Selection', cv2.WINDOW_NORMAL)
cv2.imshow('ROI_Selection', frame)

# 主檢測窗口
cv2.namedWindow('GPU_Head_Detection_WebSocket', cv2.WINDOW_NORMAL)
cv2.imshow('GPU_Head_Detection_WebSocket', result_frame)
```

## 驗證修復

### 測試步驟
1. 運行主程序：
   ```bash
   python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
   ```

2. 檢查窗口名稱：
   - 主窗口應顯示為 `GPU_Head_Detection_WebSocket`
   - ROI選取窗口應顯示為統一的英文名稱

3. 驗證功能：
   - ROI選取功能正常
   - 窗口不會分離或重複創建

## 相關文件

### 已修復的文件
- `gpu_optimized_head_detection.py` - 主程序
- `quick_test.py` - 快速測試
- `demo_new_features.py` - 新功能演示
- `tmp_rovodev_complete_fix.py` - 完整修復版本

### 未修改的文件
- `demo_multi_region.py` - 已使用英文命名
- `test_multi_region.py` - 已使用英文命名
- `test_new_features.py` - 不使用窗口顯示

## 最佳實踐

### 窗口命名建議
1. **使用描述性名稱**: 清楚表達窗口用途
2. **避免特殊字符**: 只使用字母、數字和下劃線
3. **保持一致性**: 在整個項目中使用相同的命名規範
4. **考慮長度**: 名稱要足夠短，但要清楚表達含義

### 代碼示例
```python
# 推薦的窗口命名方式
cv2.namedWindow('Main_Detection_Window', cv2.WINDOW_NORMAL)
cv2.namedWindow('ROI_Selection_Window', cv2.WINDOW_NORMAL)
cv2.namedWindow('Configuration_Panel', cv2.WINDOW_NORMAL)

# 避免的命名方式
cv2.namedWindow('主檢測窗口', cv2.WINDOW_NORMAL)  # 中文
cv2.namedWindow('window-1', cv2.WINDOW_NORMAL)    # 不描述性
cv2.namedWindow('w', cv2.WINDOW_NORMAL)           # 太短
```

---

**狀態**: ✅ 已修復  
**測試**: ✅ 需要驗證  
**影響**: 🔄 提高穩定性和兼容性