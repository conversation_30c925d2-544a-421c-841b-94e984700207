# PyTorch 2.6 緊急修復指南

## 🚨 如果仍然遇到錯誤

如果你看到類似以下的錯誤信息：

```
ERROR: Weights only load failed. This file can still be loaded...
WeightsUnpickler error: Unsupported global: GLOBAL numpy.core.multiarray._reconstruct...
```

請按照以下步驟進行緊急修復：

## 🔧 方法 1: 執行緊急修復腳本

```bash
python tmp_rovodev_emergency_fix.py
```

然後重新運行你的程序。

## 🔧 方法 2: 手動下載模型文件

1. 下載 YOLOv5 模型文件到當前目錄：
   ```bash
   # 下載 YOLOv5s 模型
   wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.pt
   
   # 或下載 YOLOv5m 模型
   wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5m.pt
   ```

2. 確保模型文件在程序運行目錄中

## 🔧 方法 3: 修改代碼（臨時解決方案）

在程序開始處添加以下代碼：

```python
import torch
import os
import warnings

# 緊急修復 PyTorch 2.6
def emergency_fix():
    # 設置環境變量
    os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = '1'
    
    # 修補 torch.load
    original_load = torch.load
    def patched_load(*args, **kwargs):
        kwargs.setdefault('weights_only', False)
        return original_load(*args, **kwargs)
    torch.load = patched_load
    
    # 修補 torch.hub.load
    original_hub_load = torch.hub.load
    def patched_hub_load(*args, **kwargs):
        kwargs.update({
            'weights_only': False,
            'trust_repo': True,
            'verbose': False
        })
        return original_hub_load(*args, **kwargs)
    torch.hub.load = patched_hub_load
    
    # 禁用警告
    warnings.filterwarnings("ignore", category=FutureWarning)
    warnings.filterwarnings("ignore", category=UserWarning)

# 在導入其他模組前執行
emergency_fix()
```

## 🔧 方法 4: 降級 PyTorch（推薦）

如果上述方法都不起作用，建議降級到 PyTorch 2.5.x：

```bash
pip uninstall torch torchvision
pip install torch==2.5.1 torchvision==0.20.1
```

## 🔧 方法 5: 清理緩存

清理 PyTorch Hub 緩存：

```python
import torch
import shutil
import os

# 獲取緩存目錄
cache_dir = torch.hub.get_dir()
print(f"緩存目錄: {cache_dir}")

# 刪除緩存（謹慎操作）
if os.path.exists(cache_dir):
    shutil.rmtree(cache_dir)
    print("緩存已清理")
```

或手動刪除：
- Windows: `%USERPROFILE%\.cache\torch\hub`
- Linux/Mac: `~/.cache/torch/hub`

## 🔧 方法 6: 使用 Conda 環境

創建新的 Conda 環境並安裝舊版本：

```bash
conda create -n pytorch25 python=3.9
conda activate pytorch25
conda install pytorch==2.5.1 torchvision==0.20.1 -c pytorch
pip install -r requirements.txt
```

## 📋 檢查修復是否成功

運行以下測試代碼：

```python
import torch
print(f"PyTorch 版本: {torch.__version__}")

try:
    model = torch.hub.load('ultralytics/yolov5', 'yolov5n', pretrained=True)
    print("✅ 模型加載成功！")
    del model
except Exception as e:
    print(f"❌ 仍然失敗: {e}")
```

## 🆘 最後手段

如果所有方法都失敗，請：

1. 檢查你的 PyTorch 版本：`python -c "import torch; print(torch.__version__)"`
2. 檢查 Python 版本：`python --version`
3. 檢查 CUDA 版本（如果使用 GPU）：`nvidia-smi`
4. 提供完整的錯誤信息以獲得進一步幫助

## 📞 聯繫支援

如果問題持續存在，請提供：
- PyTorch 版本
- Python 版本
- 操作系統
- 完整的錯誤信息
- 已嘗試的修復方法