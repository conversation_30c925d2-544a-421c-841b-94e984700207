#!/usr/bin/env python3
"""
簡化測試腳本 - 驗證 PyTorch 2.7+ 修復
"""

import torch
import warnings
import os

print(f"PyTorch 版本: {torch.__version__}")

# 應用修復
print("🔧 應用修復...")
try:
    from tmp_rovodev_ultimate_fix import ultimate_pytorch_fix
    ultimate_pytorch_fix()
    print("✅ 終極修復已應用")
except Exception as e:
    print(f"⚠️ 終極修復失敗，應用基本修復: {e}")
    
    # 基本修復
    os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = '1'
    
    if not hasattr(torch, '_basic_patched'):
        original_load = torch.load
        def basic_patched_load(*args, **kwargs):
            kwargs.setdefault('weights_only', False)
            return original_load(*args, **kwargs)
        torch.load = basic_patched_load
        torch._basic_patched = True
    
    warnings.filterwarnings("ignore")
    print("✅ 基本修復完成")

# 測試模型加載
print("\n🧪 測試模型加載...")
try:
    # 使用最簡單的參數
    model = torch.hub.load('ultralytics/yolov5', 'yolov5n', pretrained=True)
    print("✅ 模型加載成功！")
    
    # 測試推理
    dummy_input = torch.randn(1, 3, 640, 640)
    with torch.no_grad():
        results = model(dummy_input)
    print("✅ 推理測試成功！")
    
    # 清理
    del model, dummy_input, results
    print("\n🎉 所有測試通過！")
    
except Exception as e:
    print(f"❌ 測試失敗: {e}")
    print("\n💡 可能的解決方案:")
    print("1. 清理緩存: 刪除 C:\\Users\\<USER>\\.cache\\torch\\hub")
    print("2. 重新運行測試")
    print("3. 如果仍然失敗，考慮降級 PyTorch")