#!/usr/bin/env python3
"""
測試ROI區域選取邊界修正
"""

import json
import os
import logging

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_roi_region_structure():
    """測試ROI區域數據結構"""
    
    # 測試數據
    test_regions = [
        {
            'id': 0,
            'name': 'Person_Region_1',
            'type': 'person',
            'points': [[100, 100], [200, 100], [200, 200], [100, 200]],
            'closed': True
        },
        {
            'id': 1,
            'name': 'Bus_Region_1', 
            'type': 'bus',
            'points': [[300, 300], [400, 300], [400, 400], [300, 400]],
            'closed': True
        }
    ]
    
    # 測試缺少 'closed' 屬性的區域
    incomplete_region = {
        'id': 2,
        'name': 'Incomplete_Region',
        'type': 'person',
        'points': [[50, 50], [150, 50], [150, 150], [50, 150]]
        # 故意缺少 'closed' 屬性
    }
    
    test_regions.append(incomplete_region)
    
    logger.info("=== 測試ROI區域數據結構 ===")
    
    # 測試每個區域
    for i, region in enumerate(test_regions):
        logger.info(f"測試區域 {i + 1}: {region.get('name', 'Unknown')}")
        
        # 檢查必要屬性
        required_attrs = ['id', 'name', 'type', 'points']
        for attr in required_attrs:
            if attr in region:
                logger.info(f"  ✅ {attr}: {region[attr]}")
            else:
                logger.error(f"  ❌ 缺少屬性: {attr}")
        
        # 檢查 'closed' 屬性（使用 get 方法）
        closed_status = region.get('closed', True)  # 默認為 True
        logger.info(f"  ✅ closed: {closed_status} (使用 get 方法)")
        
        # 檢查點數量
        if 'points' in region and len(region['points']) > 2:
            logger.info(f"  ✅ 點數量: {len(region['points'])} (足夠)")
        else:
            logger.warning(f"  ⚠️ 點數量不足: {len(region.get('points', []))}")
        
        logger.info("")

def test_region_processing_logic():
    """測試區域處理邏輯"""
    
    logger.info("=== 測試區域處理邏輯 ===")
    
    # 模擬區域列表
    roi_regions = [
        {
            'id': 0,
            'name': 'Person_Region_1',
            'type': 'person',
            'points': [[100, 100], [200, 100], [200, 200], [100, 200]],
            'closed': True
        },
        {
            'id': 1,
            'name': 'Person_Region_2',
            'type': 'person', 
            'points': [[300, 300], [400, 300], [400, 400], [300, 400]]
            # 故意缺少 'closed' 屬性
        }
    ]
    
    # 測試處理邏輯
    for region in roi_regions:
        region_name = region.get('name', 'Unknown')
        logger.info(f"處理區域: {region_name}")
        
        # 使用修正後的邏輯
        if region.get('closed', True) and len(region['points']) > 2:
            logger.info(f"  ✅ 區域 {region_name} 可以處理")
            logger.info(f"     - closed: {region.get('closed', True)}")
            logger.info(f"     - 點數: {len(region['points'])}")
        else:
            logger.warning(f"  ⚠️ 區域 {region_name} 跳過處理")
            logger.warning(f"     - closed: {region.get('closed', True)}")
            logger.warning(f"     - 點數: {len(region['points'])}")

def test_json_save_load():
    """測試JSON保存和載入"""
    
    logger.info("=== 測試JSON保存和載入 ===")
    
    test_file = 'test_roi_regions.json'
    
    # 測試數據
    test_data = {
        'person_regions': [
            {
                'id': 0,
                'name': 'Person_Region_1',
                'type': 'person',
                'points': [[100, 100], [200, 100], [200, 200], [100, 200]],
                'closed': True
            }
        ],
        'bus_regions': [
            {
                'id': 0,
                'name': 'Bus_Region_1',
                'type': 'bus',
                'points': [[300, 300], [400, 300], [400, 400], [300, 400]],
                'closed': True
            }
        ],
        'created_time': '2024-01-01T12:00:00',
        'total_person_regions': 1,
        'total_bus_regions': 1
    }
    
    try:
        # 保存測試
        with open(test_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        logger.info(f"✅ 成功保存測試數據到 {test_file}")
        
        # 載入測試
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        logger.info(f"✅ 成功載入測試數據")
        
        # 驗證數據
        person_regions = loaded_data.get('person_regions', [])
        bus_regions = loaded_data.get('bus_regions', [])
        
        logger.info(f"載入的人頭區域數量: {len(person_regions)}")
        logger.info(f"載入的公車區域數量: {len(bus_regions)}")
        
        # 檢查每個區域的屬性
        for region in person_regions + bus_regions:
            region_name = region.get('name', 'Unknown')
            closed_status = region.get('closed', True)
            logger.info(f"  區域 {region_name}: closed={closed_status}")
        
        # 清理測試文件
        os.remove(test_file)
        logger.info(f"✅ 清理測試文件 {test_file}")
        
    except Exception as e:
        logger.error(f"❌ JSON測試失敗: {e}")
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    """主測試函數"""
    logger.info("開始ROI區域修正測試...")
    
    test_roi_region_structure()
    test_region_processing_logic()
    test_json_save_load()
    
    logger.info("=== 測試完成 ===")
    logger.info("修正要點:")
    logger.info("1. 使用 region.get('closed', True) 而不是 region['closed']")
    logger.info("2. 確保所有新創建的區域都包含 'closed' 屬性")
    logger.info("3. 在載入舊數據時自動添加缺失的屬性")
    logger.info("4. 改進錯誤處理和日誌記錄")

if __name__ == "__main__":
    main()
