#!/usr/bin/env python3
"""
修復版本的GPU優化頭部檢測系統
修復了編碼問題、重複類定義和缺失函數
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json
import subprocess
import threading
import queue

# 導入硬體解碼器
try:
    from tmp_rovodev_hardware_decoder import HardwareVideoDecoder
    HARDWARE_DECODER_AVAILABLE = True
except ImportError:
    HARDWARE_DECODER_AVAILABLE = False
    logging.warning("硬體解碼器模組不可用，將使用標準解碼")

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def detect_hardware_capabilities():
    """檢測系統硬體能力 - 修復缺失的函數"""
    capabilities = {
        'cuda_available': torch.cuda.is_available(),
        'gpu_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
        'gpu_memory': 0,
        'gpu_name': '',
        'hardware_decoder_available': HARDWARE_DECODER_AVAILABLE
    }
    
    if capabilities['cuda_available']:
        try:
            capabilities['gpu_name'] = torch.cuda.get_device_name(0)
            capabilities['gpu_memory'] = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"檢測到GPU: {capabilities['gpu_name']} ({capabilities['gpu_memory']:.1f}GB)")
        except Exception as e:
            logger.warning(f"GPU信息檢測失敗: {e}")
    
    return capabilities

def detect_hardware_encoding_capability():
    """檢測系統硬體編碼能力"""
    capabilities = {
        'ffmpeg_available': False,
        'nvidia_nvenc': False,
        'intel_qsv': False,
        'amd_amf': False,
        'recommended_codec': 'H264'
    }
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, timeout=5)
        if result.returncode == 0:
            capabilities['ffmpeg_available'] = True
            ffmpeg_output = result.stdout.decode() + result.stderr.decode()
            
            if 'nvenc' in ffmpeg_output.lower():
                capabilities['nvidia_nvenc'] = True
                capabilities['recommended_codec'] = 'h264_nvenc'
                logger.info("NVIDIA 硬體編碼可用")
            
            if 'qsv' in ffmpeg_output.lower():
                capabilities['intel_qsv'] = True
                if not capabilities['nvidia_nvenc']:
                    capabilities['recommended_codec'] = 'h264_qsv'
                logger.info("Intel Quick Sync 可用")
            
            if 'amf' in ffmpeg_output.lower():
                capabilities['amd_amf'] = True
                if not capabilities['nvidia_nvenc'] and not capabilities['intel_qsv']:
                    capabilities['recommended_codec'] = 'h264_amf'
                logger.info("AMD 硬體編碼可用")
                
        else:
            logger.warning("FFmpeg 不可用，使用 OpenCV 軟體編碼")
    except Exception as e:
        logger.warning(f"無法檢測 FFmpeg: {e}")
    
    return capabilities

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.15, device='auto', batch_size=1):
        """GPU優化的頭部檢測器"""
        self.conf_threshold = conf_threshold
        self.requested_device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # COCO類別定義
        self.coco_classes = {
            0: 'person',
            5: 'bus',
            25: 'umbrella'
        }
        
        # 自動偵測並設置最佳設備
        self.device = self._auto_detect_device()
        
        # 設置GPU優化參數
        self._setup_gpu_optimization()
        
        # 加載模型
        self._load_model()
        
        # 加載車輛檢測專用模型
        self._load_vehicle_model()
        
        # 記錄初始設備信息
        self._log_device_info()
    
    def _auto_detect_device(self):
        """自動偵測並選擇最佳運行設備"""
        if self.requested_device == 'cpu':
            logger.info("用戶指定使用CPU模式")
            return 'cpu'
        elif self.requested_device == 'cuda':
            if torch.cuda.is_available():
                logger.info("用戶指定使用CUDA GPU模式")
                return 'cuda'
            else:
                logger.warning("用戶指定CUDA但GPU不可用，自動切換到CPU模式")
                return 'cpu'
        else:  # auto
            return self._detect_best_device()
    
    def _detect_best_device(self):
        """智能偵測最佳可用設備"""
        logger.info("自動偵測最佳運行設備...")
        
        if torch.cuda.is_available():
            try:
                gpu_count = torch.cuda.device_count()
                current_gpu = torch.cuda.current_device()
                gpu_name = torch.cuda.get_device_name(current_gpu)
                gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
                
                if gpu_memory >= 2.0:
                    if self._test_gpu_performance():
                        logger.info(f"自動選擇GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                        return 'cuda'
                    else:
                        logger.warning("GPU性能測試失敗，切換到CPU模式")
                        return 'cpu'
                else:
                    logger.warning(f"GPU記憶體不足 ({gpu_memory:.1f}GB < 2.0GB)，切換到CPU模式")
                    return 'cpu'
                    
            except Exception as e:
                logger.warning(f"GPU檢測過程中出現錯誤: {e}，切換到CPU模式")
                return 'cpu'
        else:
            logger.info("CUDA不可用，使用CPU模式")
            return 'cpu'
    
    def _test_gpu_performance(self):
        """測試GPU性能"""
        try:
            logger.info("測試GPU性能...")
            test_tensor = torch.randn(1, 3, 640, 640).cuda()
            
            start_time = time.time()
            for _ in range(5):
                result = test_tensor * 2.0 + 1.0
                torch.cuda.synchronize()
            end_time = time.time()
            
            test_time = end_time - start_time
            logger.info(f"GPU基本運算測試: {test_time:.3f}秒")
            
            del test_tensor, result
            torch.cuda.empty_cache()
            
            if test_time < 1.0:
                logger.info("GPU性能測試通過")
                return True
            else:
                logger.warning(f"GPU性能較慢 ({test_time:.3f}s)")
                return False
                
        except Exception as e:
            logger.warning(f"GPU性能測試失敗: {e}")
            return False
    
    def _setup_gpu_optimization(self):
        """設置GPU優化參數"""
        if self.device == 'cuda':
            try:
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                torch.cuda.empty_cache()
                
                gpu_count = torch.cuda.device_count()
                current_gpu = torch.cuda.current_device()
                gpu_name = torch.cuda.get_device_name(current_gpu)
                gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
                
                logger.info(f"GPU優化設置:")
                logger.info(f"  - GPU數量: {gpu_count}")
                logger.info(f"  - 當前GPU: {current_gpu} ({gpu_name})")
                logger.info(f"  - GPU總記憶體: {gpu_memory:.1f} GB")
                logger.info(f"  - CUDA版本: {torch.version.cuda}")
                
                # 根據GPU記憶體調整批次大小
                if gpu_memory < 4.0:
                    self.batch_size = 1
                elif gpu_memory < 8.0:
                    self.batch_size = min(self.batch_size, 2)
                
            except Exception as e:
                logger.error(f"GPU優化設置失敗: {e}")
                self.device = 'cpu'
                self._setup_cpu_optimization()
        else:
            self._setup_cpu_optimization()
    
    def _setup_cpu_optimization(self):
        """設置CPU優化參數"""
        logger.info("CPU優化設置:")
        logger.info(f"  - 使用設備: CPU")
        logger.info(f"  - 批次大小: {self.batch_size}")
        
        torch.set_num_threads(4)
        logger.info(f"  - CPU線程數: {torch.get_num_threads()}")
    
    def _log_device_info(self):
        """記錄設備信息"""
        logger.info("=" * 50)
        logger.info("設備配置摘要:")
        logger.info(f"  - 請求設備: {self.requested_device}")
        logger.info(f"  - 實際使用: {self.device.upper()}")
        logger.info(f"  - 模型類型: {self.model_type}")
        logger.info(f"  - 批次大小: {self.batch_size}")
        logger.info(f"  - 置信度閾值: {self.conf_threshold}")
        logger.info("=" * 50)
    
    def _load_model(self):
        """加載模型"""
        try:
            logger.info("加載YOLOv5模型...")
            
            if self.model_type == 'custom':
                try:
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                    logger.info("加載專門的頭部檢測模型")
                except Exception:
                    logger.warning("未找到頭部檢測模型，使用人體檢測模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', pretrained=True)
                    self.model_type = 'person'
            else:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', pretrained=True)
            
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.5
            self.model.max_det = 1000
            
            if self.device == 'cuda':
                self.model.half()
                logger.info("啟用FP16半精度推理")
                self._warmup_gpu()
            
            logger.info(f"模型加載完成，設備: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加載失敗: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU預熱"""
        logger.info("GPU預熱中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU預熱完成")
    
    def _load_vehicle_model(self):
        """加載車輛檢測模型"""
        try:
            logger.info("加載車輛檢測專用模型...")
            
            self.vehicle_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.vehicle_model.to(self.device)
            self.vehicle_model.conf = 0.3
            self.vehicle_model.iou = 0.45
            self.vehicle_model.max_det = 1000
            
            if self.device == 'cuda':
                self.vehicle_model.half()
                self._warmup_vehicle_gpu()
            
            logger.info(f"車輛檢測模型加載完成")
            
        except Exception as e:
            logger.error(f"車輛檢測模型加載失敗: {e}")
            self.vehicle_model = self.model

    def _warmup_vehicle_gpu(self):
        """車輛檢測模型GPU預熱"""
        logger.info("車輛檢測模型GPU預熱中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(2):
                _ = self.vehicle_model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("車輛檢測模型GPU預熱完成")

def main():
    """主函數 - 簡化版本用於測試"""
    parser = argparse.ArgumentParser(description='修復版GPU優化頭部檢測系統')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['cpu', 'cuda', 'auto'], help='運行設備')
    parser.add_argument('--test-only', action='store_true',
                       help='僅測試模型加載，不運行檢測')
    
    args = parser.parse_args()
    
    try:
        # 檢測硬體能力
        capabilities = detect_hardware_capabilities()
        logger.info(f"系統硬體能力: {capabilities}")
        
        # 檢測編碼能力
        encoding_caps = detect_hardware_encoding_capability()
        logger.info(f"編碼能力: {encoding_caps}")
        
        # 創建檢測器
        detector = GPUOptimizedHeadDetector(
            model_type='person',
            conf_threshold=0.15,
            device=args.device,
            batch_size=1
        )
        
        logger.info("檢測器初始化成功！")
        
        if args.test_only:
            logger.info("測試模式 - 僅驗證模型加載")
            return 0
        
        # 這裡可以添加實際的檢測邏輯
        logger.info("如需完整功能，請使用原始文件")
        
    except Exception as e:
        logger.error(f"程序運行出錯: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())