#!/usr/bin/env python3
"""
GPU优化的YOLOv5头部检测系统
专门针对GPU加速优化的WebSocket视频流头部检测
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json
import subprocess
import threading
import queue
import warnings
import websocket
import base64
from io import BytesIO
from PIL import Image

# 導入 PyTorch 2.7+ 終極修復
try:
    from tmp_rovodev_ultimate_fix import ultimate_pytorch_fix
    ultimate_pytorch_fix()
    logging.info("✅ 已應用 PyTorch 2.7+ 終極修復")
    PYTORCH_FIX_AVAILABLE = True
except ImportError:
    logging.warning("⚠️ 終極修復模組不可用，嘗試標準修復...")
    PYTORCH_FIX_AVAILABLE = False
    
    # 嘗試標準修復
    try:
        from tmp_rovodev_pytorch26_fix import safe_torch_hub_load, apply_pytorch26_fixes
        apply_pytorch26_fixes()
        logging.info("✅ 已應用標準修復")
        PYTORCH_FIX_AVAILABLE = True
    except ImportError:
        logging.warning("⚠️ 標準修復模組也不可用")
        
        # 最基本的修復
        pytorch_version = torch.__version__
        major, minor = map(int, pytorch_version.split('.')[:2])
        if major > 2 or (major == 2 and minor >= 6):
            logging.warning(f"🚨 檢測到 PyTorch {pytorch_version}，應用基本修復")
            
            # 基本修復
            import os
            os.environ.update({
                'TORCH_SERIALIZATION_SAFE_GLOBALS': '1',
                'PYTORCH_DISABLE_SAFE_GLOBALS': '1'
            })
            
            # 修補 torch.load
            if not hasattr(torch, '_patched_load'):
                original_load = torch.load
                def patched_load(*args, **kwargs):
                    kwargs.setdefault('weights_only', False)
                    return original_load(*args, **kwargs)
                torch.load = patched_load
                torch._patched_load = True
            
            warnings.filterwarnings("ignore", category=FutureWarning, module="torch")
            warnings.filterwarnings("ignore", category=UserWarning, module="torch")
            warnings.filterwarnings("ignore", message=".*weights_only.*")
            logging.info("✅ 已應用基本修復")

# 導入硬體解碼器
try:
    from tmp_rovodev_hardware_decoder import HardwareVideoDecoder
    HARDWARE_DECODER_AVAILABLE = True
except ImportError:
    HARDWARE_DECODER_AVAILABLE = False
    logging.warning("⚠️ 硬體解碼器模組不可用，將使用標準解碼")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HardwareVideoWriter:
    """硬體加速視頻寫入器"""
    
    def __init__(self, output_path, fps, width, height, codec='auto', quality='medium'):
        self.output_path = output_path
        self.fps = fps
        self.width = width
        self.height = height
        self.quality = quality
        self.process = None
        self.frame_queue = queue.Queue(maxsize=30)
        self.writer_thread = None
        self.is_running = False
        
        # 檢測可用的硬體編碼器
        self.codec = self._detect_best_codec() if codec == 'auto' else codec
        
        # 品質設定
        self.quality_settings = {
            'high': {'crf': 18, 'preset': 'medium'},
            'medium': {'crf': 23, 'preset': 'fast'},
            'low': {'crf': 28, 'preset': 'faster'},
            'very_low': {'crf': 32, 'preset': 'veryfast'}
        }
        
        self._start_ffmpeg_process()
    
    def _detect_best_codec(self):
        """檢測最佳可用的硬體編碼器"""
        codecs_to_test = [
            ('h264_nvenc', 'NVIDIA GPU 硬體編碼'),
            ('h264_qsv', 'Intel Quick Sync 硬體編碼'),
            ('h264_amf', 'AMD GPU 硬體編碼'),
            ('libx264', '軟體編碼 (高品質)')
        ]
        
        for codec, description in codecs_to_test:
            if self._test_codec(codec):
                logger.info(f"🚀 使用 {codec}: {description}")
                return codec
        
        logger.warning("⚠️ 無可用硬體編碼器，使用預設軟體編碼")
        return 'libx264'
    
    def _test_codec(self, codec):
        """測試編碼器是否可用"""
        try:
            cmd = [
                'ffmpeg', '-f', 'lavfi', '-i', 'testsrc=duration=1:size=320x240:rate=1',
                '-c:v', codec, '-f', 'null', '-'
            ]
            result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def _start_ffmpeg_process(self):
        """啟動 FFmpeg 程序"""
        settings = self.quality_settings.get(self.quality, self.quality_settings['medium'])
        
        cmd = [
            'ffmpeg', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-s', f'{self.width}x{self.height}', '-pix_fmt', 'bgr24',
            '-r', str(self.fps), '-i', '-', '-c:v', self.codec,
        ]
        
        # 根據編碼器類型添加參數
        if 'nvenc' in self.codec:
            cmd.extend(['-preset', 'fast', '-cq', str(settings['crf']), '-b:v', '0'])
        elif 'qsv' in self.codec:
            cmd.extend(['-preset', 'fast', '-global_quality', str(settings['crf'])])
        elif 'amf' in self.codec:
            cmd.extend(['-quality', 'speed', '-rc', 'cqp', '-qp_i', str(settings['crf'])])
        else:
            cmd.extend(['-preset', settings['preset'], '-crf', str(settings['crf'])])
        
        cmd.extend(['-pix_fmt', 'yuv420p', '-movflags', '+faststart', self.output_path])
        
        try:
            self.process = subprocess.Popen(cmd, stdin=subprocess.PIPE, 
                                          stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
            
            self.is_running = True
            self.writer_thread = threading.Thread(target=self._writer_worker)
            self.writer_thread.daemon = True
            self.writer_thread.start()
            
            logger.info(f"✅ FFmpeg 硬體編碼器已啟動: {self.codec}")
            logger.info(f"   輸出: {self.output_path}")
            logger.info(f"   設定: {self.width}x{self.height} @ {self.fps}fps, CRF: {settings['crf']}")
            
        except Exception as e:
            logger.error(f"❌ 無法啟動 FFmpeg: {e}")
            self.process = None
    
    def _writer_worker(self):
        """寫入執行緒工作函數"""
        while self.is_running or not self.frame_queue.empty():
            try:
                frame = self.frame_queue.get(timeout=1.0)
                if frame is None:
                    break
                
                if self.process and self.process.stdin:
                    self.process.stdin.write(frame.tobytes())
                    self.process.stdin.flush()
                
                self.frame_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"寫入幀時出錯: {e}")
                break
    
    def write(self, frame):
        """寫入一幀"""
        if not self.is_running or not self.process:
            return False
        
        if frame.shape[:2] != (self.height, self.width):
            frame = cv2.resize(frame, (self.width, self.height))
        
        try:
            self.frame_queue.put_nowait(frame.copy())
            return True
        except queue.Full:
            logger.warning("⚠️ 編碼佇列已滿，跳過此幀")
            return False
    
    def release(self):
        """釋放資源"""
        if not self.is_running:
            return
        
        logger.info("正在關閉硬體編碼器...")
        self.is_running = False
        
        try:
            self.frame_queue.put_nowait(None)
        except queue.Full:
            pass
        
        if self.writer_thread:
            self.writer_thread.join(timeout=5.0)
        
        if self.process:
            try:
                self.process.stdin.close()
                self.process.wait(timeout=10.0)
            except:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5.0)
                except:
                    self.process.kill()
        
        logger.info("✅ 硬體編碼器已關閉")
    
    def isOpened(self):
        """檢查是否已開啟"""
        return self.is_running and self.process is not None

def detect_hardware_encoding_capability():
    """檢測系統硬體編碼能力"""
    capabilities = {
        'ffmpeg_available': False,
        'nvidia_nvenc': False,
        'intel_qsv': False,
        'amd_amf': False,
        'recommended_codec': 'H264'
    }
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE, timeout=5)
        if result.returncode == 0:
            capabilities['ffmpeg_available'] = True
            ffmpeg_output = result.stdout.decode() + result.stderr.decode()
            
            if 'nvenc' in ffmpeg_output.lower():
                capabilities['nvidia_nvenc'] = True
                capabilities['recommended_codec'] = 'h264_nvenc'
                logger.info("✅ NVIDIA 硬體編碼可用")
            
            if 'qsv' in ffmpeg_output.lower():
                capabilities['intel_qsv'] = True
                if not capabilities['nvidia_nvenc']:
                    capabilities['recommended_codec'] = 'h264_qsv'
                logger.info("✅ Intel Quick Sync 可用")
            
            if 'amf' in ffmpeg_output.lower():
                capabilities['amd_amf'] = True
                if not capabilities['nvidia_nvenc'] and not capabilities['intel_qsv']:
                    capabilities['recommended_codec'] = 'h264_amf'
                logger.info("✅ AMD 硬體編碼可用")
                
        else:
            logger.warning("⚠️ FFmpeg 不可用，使用 OpenCV 軟體編碼")
    except Exception as e:
        logger.warning(f"⚠️ 無法檢測 FFmpeg: {e}")
    
    return capabilities

class GPUOptimizedWebSocketProcessor:
    """GPU優化的WebSocket視頻流處理器 - 支援WebSocket視頻接收"""

    def __init__(self, websocket_url, detector, save_video=False, output_path='output.mp4',
                 detection_interval=10, video_quality='medium', target_fps=None,
                 target_resolution=None, use_hardware_encoding=True):
        """
        初始化WebSocket處理器

        Args:
            websocket_url: WebSocket流地址 (例如: ws://*************:8004)
            detector: 檢測器實例
            save_video: 是否保存視頻
            output_path: 輸出路徑
            detection_interval: 檢測間隔（幀數）
            video_quality: 視頻品質
            target_fps: 目標FPS
            target_resolution: 目標解析度
            use_hardware_encoding: 使用硬體編碼
        """
        self.websocket_url = websocket_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.detection_interval = detection_interval
        self.video_quality = video_quality
        self.target_fps = target_fps
        self.target_resolution = target_resolution
        self.use_hardware_encoding = use_hardware_encoding

        # WebSocket相關
        self.ws = None
        self.out = None
        self.frame_queue = queue.Queue(maxsize=30)
        self.is_connected = False
        self.connection_thread = None

        # 檢測硬體能力
        self.hardware_capabilities = detect_hardware_encoding_capability()

        # ROI和統計相關
        self.roi_regions = []
        self.bus_regions = []
        self.roi_file = 'multi_roi_regions.json'
        self.current_region_points = []
        self.current_region_closed = False
        self.current_region_type = 'person'
        self.is_drawing_roi = False  # ROI繪製狀態
        self.selected_region_index = -1
        self.selected_region_type = 'person'
        
        # 編輯模式相關屬性
        self.edit_point_index = -1  # 當前編輯的點索引
        self.drag_mode = False  # 拖拽模式
        self.edit_threshold = 15  # 編輯點選擇閾值

        # 編輯模式相關
        self.edit_mode = False
        self.edit_point_index = -1
        self.drag_mode = False

        # 統計數據
        self.region_statistics = {}
        self.bus_statistics = {}
        self.total_statistics = {
            'total_count': 0,
            'region_counts': [],
            'detection_history': []
        }

        # 性能監控
        self.frame_times = []
        self.gpu_memory_usage = []
        self.detection_frame_count = 0

        # 檢測結果緩存
        self.last_detection_result = None  # 緩存上次檢測結果

        # 顯示控制
        self.display_enabled = True
        self.show_websocket_image = True  # 是否顯示WebSocket圖片
        self.edit_mode = False  # 編輯模式狀態
        self.window_name = "GPU_Head_Detection_WebSocket"
        self.window_created = False
        self.last_display_toggle_time = 0

        # 視頻屬性
        self.original_fps = 25  # 預設FPS
        self.original_width = 640  # 預設寬度
        self.original_height = 480  # 預設高度

        # 載入已保存的ROI區域
        self._load_multi_roi()

        logger.info("🚀 GPU優化WebSocket處理器初始化完成")

    def connect_websocket(self):
        """連接WebSocket流"""
        try:
            logger.info(f"正在連接WebSocket: {self.websocket_url}")

            def on_message(ws, message):
                """處理WebSocket消息"""
                try:
                    if message.startswith('data:image/jpeg;base64,'):
                        # 解碼base64圖像
                        base64_data = message.split(',')[1]
                        image_data = base64.b64decode(base64_data)

                        # 轉換為OpenCV格式
                        image = Image.open(BytesIO(image_data))
                        frame = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                        # 更新視頻屬性
                        if frame is not None:
                            self.original_height, self.original_width = frame.shape[:2]

                        # 將幀放入隊列
                        if not self.frame_queue.full():
                            self.frame_queue.put(frame)
                        else:
                            # 如果隊列滿了，丟棄最舊的幀
                            try:
                                self.frame_queue.get_nowait()
                                self.frame_queue.put(frame)
                            except queue.Empty:
                                pass

                except Exception as e:
                    logger.error(f"處理WebSocket消息時出錯: {e}")

            def on_error(ws, error):
                """處理WebSocket錯誤"""
                logger.error(f"WebSocket錯誤: {error}")
                self.is_connected = False

            def on_close(ws, close_status_code, close_msg):
                """處理WebSocket關閉"""
                logger.info("WebSocket連接已關閉")
                self.is_connected = False

            def on_open(ws):
                """處理WebSocket開啟"""
                logger.info("✅ WebSocket連接成功")
                self.is_connected = True

            # 創建WebSocket連接
            self.ws = websocket.WebSocketApp(
                self.websocket_url,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_open=on_open
            )

            # 在單獨的線程中運行WebSocket
            self.connection_thread = threading.Thread(target=self.ws.run_forever)
            self.connection_thread.daemon = True
            self.connection_thread.start()

            # 等待連接建立
            timeout = 10  # 10秒超時
            start_time = time.time()
            while not self.is_connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if not self.is_connected:
                raise Exception("WebSocket連接超時")

            logger.info(f"WebSocket流連接成功: {self.original_width}x{self.original_height}")

            # 如果需要保存視頻，初始化視頻寫入器
            if self.save_video:
                if self.use_hardware_encoding and self.hardware_capabilities['ffmpeg_available']:
                    self.out = HardwareVideoWriter(
                        self.output_path,
                        self.original_fps,
                        self.original_width,
                        self.original_height,
                        codec=self.hardware_capabilities['recommended_codec'],
                        quality=self.video_quality
                    )
                    logger.info(f"✅ 硬體編碼視頻寫入器已初始化: {self.output_path}")
                else:
                    fourcc = cv2.VideoWriter_fourcc(*'H264')
                    self.out = cv2.VideoWriter(
                        self.output_path,
                        fourcc,
                        self.original_fps,
                        (self.original_width, self.original_height)
                    )
                    logger.info(f"✅ 標準視頻寫入器已初始化: {self.output_path}")

            return True

        except Exception as e:
            logger.error(f"連接WebSocket流失敗: {e}")
            return False

    def get_frame(self):
        """從WebSocket獲取幀"""
        try:
            if not self.frame_queue.empty():
                return True, self.frame_queue.get_nowait()
            else:
                return False, None
        except queue.Empty:
            return False, None

    def process_stream(self, display=True, max_frames=None):
        """GPU優化的WebSocket流處理"""
        if not self.connect_websocket():
            return

        # 初始顯示設定
        self.display_enabled = display
        if self.display_enabled:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)
            self.window_created = True

        frame_count = 0
        start_time = time.time()

        try:
            while self.is_connected:
                loop_start_time = time.time()

                ret, frame = self.get_frame()
                if not ret or frame is None:
                    time.sleep(0.01)  # 短暫等待新幀
                    continue

                frame_count += 1

                # 檢測處理
                if frame_count % self.detection_interval == 0 or self.last_detection_result is None:
                    self.detection_frame_count += 1
                    detection_start = time.time()

                    # 執行多區域檢測
                    detection_results = self.detector.detect_heads_batch(
                        [frame],
                        roi_regions=self.roi_regions,
                        bus_regions=self.bus_regions
                    )[0]

                    detection_time = time.time() - detection_start
                    self.frame_times.append(detection_time)

                    # 更新統計數據
                    self._update_statistics(detection_results)
                    self.last_detection_result = detection_results
                else:
                    # 使用緩存的檢測結果，但重新繪製
                    if self.last_detection_result:
                        detection_results = self.last_detection_result.copy()
                        detection_results['annotated_frame'] = self.detector._draw_multi_region_results(
                            frame,
                            detection_results['region_results'],
                            detection_results['total_count'],
                            detection_results['bus_results'],
                            detection_results['bus_count']
                        )
                    else:
                        detection_results = {
                            'total_count': 0,
                            'region_results': [],
                            'bus_count': 0,
                            'bus_results': [],
                            'annotated_frame': frame
                        }

                annotated_frame = detection_results['annotated_frame']

                # 繪製ROI區域
                annotated_frame = self._draw_roi_regions(annotated_frame)

                # 保存視頻
                if self.save_video and self.out:
                    if hasattr(self.out, 'write') and callable(self.out.write):
                        if not self.out.write(annotated_frame):
                            logger.warning("⚠️ 視頻寫入失敗")
                    else:
                        self.out.write(annotated_frame)

                # 顯示處理
                if self.display_enabled and self.window_created:
                    try:
                        if self.show_websocket_image:
                            # 顯示WebSocket圖片
                            cv2.imshow(self.window_name, annotated_frame)
                        else:
                            # 顯示檢測數量統計畫面
                            stats_frame = self._create_stats_frame(annotated_frame.shape, detection_results)
                            cv2.imshow(self.window_name, stats_frame)
                        key = cv2.waitKey(1) & 0xFF

                        # 處理按鍵
                        if key == ord('q'):
                            logger.info("用戶按下 'q'，退出程序")
                            break
                        elif key == ord('v'):
                            self._toggle_websocket_display()
                        elif key == ord('d'):
                            self._toggle_display()
                        elif key == ord('n'):
                            self._add_new_roi_region()
                        elif key == ord('b'):
                            self._add_new_bus_region()
                        elif key == ord('h'):
                            self._show_help()
                        elif key == ord('s'):
                            self._save_multi_roi()
                        elif key == ord('c'):
                            self._clear_current_region()
                        elif key == ord('r'):
                            self._reset_all_regions()
                        elif key == ord('e'):
                            self._toggle_edit_mode()
                        elif key == ord('t'):
                            self._toggle_region_type()
                        elif key >= ord('1') and key <= ord('9'):
                            region_index = key - ord('1')
                            self._select_region_for_edit(region_index)
                    except cv2.error as e:
                        logger.warning(f"OpenCV顯示錯誤: {e}")
                        if "destroyed" in str(e).lower():
                            self.window_created = False

                # 性能監控
                loop_time = time.time() - loop_start_time
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)

                # 限制處理速度
                if max_frames and frame_count >= max_frames:
                    logger.info(f"達到最大幀數限制: {max_frames}")
                    break

                # 每100幀顯示一次統計
                if frame_count % 100 == 0:
                    self._log_performance_stats(frame_count, start_time)

        except KeyboardInterrupt:
            logger.info("收到中斷信號，正在退出...")
        except Exception as e:
            logger.error(f"處理流時出錯: {e}")
        finally:
            self._cleanup()

    def _cleanup(self):
        """清理資源"""
        logger.info("正在清理資源...")

        # 關閉WebSocket連接
        if self.ws:
            self.ws.close()

        self.is_connected = False

        # 等待連接線程結束
        if self.connection_thread and self.connection_thread.is_alive():
            self.connection_thread.join(timeout=2.0)

        # 關閉視頻寫入器
        if self.out:
            if hasattr(self.out, 'release'):
                self.out.release()
            else:
                self.out = None

        # 關閉顯示窗口
        if self.window_created:
            cv2.destroyAllWindows()
            self.window_created = False

        logger.info("✅ 資源清理完成")

    def _load_multi_roi(self):
        """從文件加載多個ROI區域"""
        # 初始化公車區域
        self.bus_regions = []

        if os.path.exists(self.roi_file):
            try:
                with open(self.roi_file, 'r') as f:
                    data = json.load(f)
                    # 兼容舊格式：如果是點列表，轉換為新格式
                    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], list):
                        logger.info("檢測到舊格式ROI文件，正在轉換...")
                        self.roi_regions = [{
                            'id': 0,
                            'name': 'Person_Region_1',
                            'type': 'person',
                            'points': data,
                            'closed': True
                        }]
                        self.bus_regions = []
                    else:
                        # 新格式：分別載入人頭和公車區域
                        self.roi_regions = data.get('person_regions', [])
                        self.bus_regions = data.get('bus_regions', [])

                        # 確保每個區域都有必要的屬性
                        for i, region in enumerate(self.roi_regions):
                            if 'id' not in region:
                                region['id'] = i
                            if 'name' not in region:
                                region['name'] = f'Person_Region_{i+1}'
                            if 'type' not in region:
                                region['type'] = 'person'
                            if 'closed' not in region:
                                region['closed'] = True

                        for i, region in enumerate(self.bus_regions):
                            if 'id' not in region:
                                region['id'] = i
                            if 'name' not in region:
                                region['name'] = f'Bus_Region_{i+1}'
                            if 'type' not in region:
                                region['type'] = 'bus'
                            if 'closed' not in region:
                                region['closed'] = True

                logger.info(f"載入 {len(self.roi_regions)} 個人頭檢測區域和 {len(self.bus_regions)} 個公車檢測區域")

            except Exception as e:
                logger.error(f"載入ROI區域失敗: {e}")
                self.roi_regions = []
                self.bus_regions = []
        else:
            logger.info("未找到ROI區域文件，將創建新的區域")
            self.roi_regions = []
            self.bus_regions = []

    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數用於繪製多個ROI區域"""
        if event == cv2.EVENT_LBUTTONDOWN:
            if not self.current_region_closed:
                self.current_region_points.append([x, y])
                logger.info(f"添加點: ({x}, {y})")
        elif event == cv2.EVENT_RBUTTONDOWN:
            if len(self.current_region_points) >= 3 and not self.current_region_closed:
                self._close_current_region()

    def _close_current_region(self):
        """關閉當前區域並保存"""
        if len(self.current_region_points) >= 3:
            region_data = {
                'id': len(self.roi_regions) + len(self.bus_regions),
                'name': f'{self.current_region_type.title()}_Region_{len(self.roi_regions if self.current_region_type == "person" else self.bus_regions) + 1}',
                'type': self.current_region_type,
                'points': self.current_region_points.copy(),
                'closed': True
            }

            if self.current_region_type == 'person':
                self.roi_regions.append(region_data)
                logger.info(f"✅ 人頭檢測區域已創建: {region_data['name']} ({len(self.current_region_points)} 個點)")
            else:  # bus
                self.bus_regions.append(region_data)
                logger.info(f"✅ 公車檢測區域已創建: {region_data['name']} ({len(self.current_region_points)} 個點)")

            self.current_region_points = []
            self.current_region_closed = True
            self._save_multi_roi()

    def _save_multi_roi(self):
        """保存多個ROI區域到文件"""
        try:
            data = {
                'person_regions': self.roi_regions,
                'bus_regions': self.bus_regions,
                'created_time': datetime.now().isoformat(),
                'total_person_regions': len(self.roi_regions),
                'total_bus_regions': len(self.bus_regions)
            }

            with open(self.roi_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"✅ ROI區域已保存: {len(self.roi_regions)} 個人頭區域, {len(self.bus_regions)} 個公車區域")

        except Exception as e:
            logger.error(f"保存ROI區域失敗: {e}")

    def _draw_roi_regions(self, frame):
        """繪製所有ROI區域 - 改進的視覺反饋"""
        # 繪製人頭檢測區域（紅色）
        for i, region in enumerate(self.roi_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                points = np.array(region['points'], dtype=np.int32)

                # 檢查是否為選中的區域
                is_selected = (self.selected_region_type == 'person' and
                             self.selected_region_index == i)

                # 根據選中狀態調整顏色和線寬
                if is_selected:
                    color = (0, 255, 255)  # 黃色表示選中
                    thickness = 4
                    # 繪製選中區域的高亮邊框
                    cv2.polylines(frame, [points], True, (255, 255, 255), 6)  # 白色外框
                else:
                    color = (0, 0, 255)  # 紅色
                    thickness = 2

                cv2.polylines(frame, [points], True, color, thickness)

                # 添加區域標籤
                if len(points) > 0:
                    label_pos = tuple(points[0])
                    label_text = region.get('name', f'Person_{i+1}')
                    if is_selected:
                        label_text += " [選中]"
                    cv2.putText(frame, label_text, label_pos,
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                    # 如果選中，繪製頂點
                    if is_selected:
                        for point in points:
                            cv2.circle(frame, tuple(point), 6, (0, 255, 255), -1)
                            cv2.circle(frame, tuple(point), 8, (255, 255, 255), 2)

        # 繪製公車檢測區域（藍色）
        for i, region in enumerate(self.bus_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                points = np.array(region['points'], dtype=np.int32)

                # 檢查是否為選中的區域
                is_selected = (self.selected_region_type == 'bus' and
                             self.selected_region_index == i)

                # 根據選中狀態調整顏色和線寬
                if is_selected:
                    color = (255, 255, 0)  # 青色表示選中
                    thickness = 4
                    # 繪製選中區域的高亮邊框
                    cv2.polylines(frame, [points], True, (255, 255, 255), 6)  # 白色外框
                else:
                    color = (255, 0, 0)  # 藍色
                    thickness = 2

                cv2.polylines(frame, [points], True, color, thickness)

                # 添加區域標籤
                if len(points) > 0:
                    label_pos = tuple(points[0])
                    label_text = region.get('name', f'Bus_{i+1}')
                    if is_selected:
                        label_text += " [選中]"
                    cv2.putText(frame, label_text, label_pos,
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

                    # 如果選中，繪製頂點
                    if is_selected:
                        for point in points:
                            cv2.circle(frame, tuple(point), 6, (255, 255, 0), -1)
                            cv2.circle(frame, tuple(point), 8, (255, 255, 255), 2)

        # 繪製當前正在創建的區域
        if len(self.current_region_points) > 0:
            points = np.array(self.current_region_points, dtype=np.int32)
            color = (0, 255, 255) if self.current_region_type == 'person' else (255, 255, 0)

            if len(points) > 1:
                cv2.polylines(frame, [points], False, color, 3)

            # 繪製已添加的點
            for i, point in enumerate(points):
                cv2.circle(frame, tuple(point), 6, color, -1)
                cv2.circle(frame, tuple(point), 8, (255, 255, 255), 2)
                # 添加點序號
                cv2.putText(frame, str(i+1), (point[0]+10, point[1]-10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return frame

    def _toggle_display(self):
        """切換顯示開關"""
        current_time = time.time()
        if current_time - self.last_display_toggle_time > 0.5:  # 防止快速切換
            self.display_enabled = not self.display_enabled
            self.last_display_toggle_time = current_time
            status = "開啟" if self.display_enabled else "關閉"
            logger.info(f"顯示已{status}")

            if not self.display_enabled and self.window_created:
                cv2.destroyWindow(self.window_name)
                self.window_created = False
            elif self.display_enabled and not self.window_created:
                cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
                cv2.setMouseCallback(self.window_name, self._mouse_callback)
                self.window_created = True

    def _toggle_websocket_display(self):
        """切換WebSocket圖片顯示，關閉時顯示檢測數量"""
        if not hasattr(self, 'show_websocket_image'):
            self.show_websocket_image = True
        
        self.show_websocket_image = not self.show_websocket_image
        status = "顯示WebSocket圖片" if self.show_websocket_image else "顯示檢測數量"
        logger.info(f"切換到: {status}")
        
        if not self.show_websocket_image:
            # 顯示當前檢測統計
            self._show_detection_stats()

    def _toggle_edit_mode(self):
        """切換編輯模式"""
        if not hasattr(self, 'edit_mode'):
            self.edit_mode = False
        
        self.edit_mode = not self.edit_mode
        status = "開啟" if self.edit_mode else "關閉"
        logger.info(f"編輯模式已{status}")
        
        if self.edit_mode:
            logger.info("編輯模式: 可以選擇和修改現有ROI區域")
            logger.info("使用數字鍵 1-9 選擇區域進行編輯")
        else:
            logger.info("編輯模式已關閉，回到正常檢測模式")

    def _show_detection_stats(self):
        """顯示檢測統計信息"""
        try:
            # 計算各區域的檢測數量
            roi_count = 0
            bus_count = 0
            
            if hasattr(self, 'last_detection_result') and self.last_detection_result:
                # 統計ROI區域內的檢測數量
                for region in self.roi_regions:
                    if 'polygon' in region:
                        roi_count += self._count_detections_in_region(
                            self.last_detection_result, region['polygon']
                        )
                
                # 統計公交車區域內的檢測數量
                for region in self.bus_regions:
                    if 'polygon' in region:
                        bus_count += self._count_detections_in_region(
                            self.last_detection_result, region['polygon']
                        )
            
            # 顯示統計信息
            stats_text = f"""
=== 檢測統計信息 ===
ROI區域檢測數量: {roi_count}
公交車區域檢測數量: {bus_count}
總檢測數量: {roi_count + bus_count}
ROI區域數量: {len(self.roi_regions)}
公交車區域數量: {len(self.bus_regions)}
==================
            """
            logger.info(stats_text)
            
        except Exception as e:
            logger.error(f"顯示檢測統計時出錯: {e}")

    def _count_detections_in_region(self, detections, polygon):
        """計算指定區域內的檢測數量"""
        try:
            import cv2
            count = 0
            
            if detections and len(detections) > 0:
                for detection in detections:
                    # 獲取檢測框中心點
                    x1, y1, x2, y2 = detection[:4]
                    center_x = int((x1 + x2) / 2)
                    center_y = int((y1 + y2) / 2)
                    
                    # 檢查點是否在多邊形內
                    if cv2.pointPolygonTest(np.array(polygon, dtype=np.int32), 
                                          (center_x, center_y), False) >= 0:
                        count += 1
            
            return count
            
        except Exception as e:
            logger.error(f"計算區域內檢測數量時出錯: {e}")
            return 0

    def _create_stats_frame(self, frame_shape, detections):
        """創建檢測統計顯示畫面"""
        try:
            # 創建黑色背景
            stats_frame = np.zeros(frame_shape, dtype=np.uint8)
            
            # 計算檢測統計
            roi_count = 0
            bus_count = 0
            total_detections = len(detections) if detections else 0
            
            # 統計各區域檢測數量
            for region in self.roi_regions:
                if 'polygon' in region:
                    roi_count += self._count_detections_in_region(detections, region['polygon'])
            
            for region in self.bus_regions:
                if 'polygon' in region:
                    bus_count += self._count_detections_in_region(detections, region['polygon'])
            
            # 設置文字參數
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1.2
            color = (255, 255, 255)  # 白色
            thickness = 2
            line_height = 60
            
            # 添加標題
            title = "Detection Statistics"
            cv2.putText(stats_frame, title, (50, 80), font, 1.5, (0, 255, 255), 3)
            
            # 添加統計信息
            y_pos = 160
            stats_info = [
                f"Total Detections: {total_detections}",
                f"ROI Regions: {len(self.roi_regions)} areas",
                f"ROI Detections: {roi_count}",
                f"Bus Regions: {len(self.bus_regions)} areas", 
                f"Bus Detections: {bus_count}",
                f"",
                f"Press 'v' to show WebSocket image",
                f"Press 'h' for help"
            ]
            
            for info in stats_info:
                if info:  # 跳過空行
                    cv2.putText(stats_frame, info, (50, y_pos), font, font_scale, color, thickness)
                y_pos += line_height
            
            # 添加時間戳
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(stats_frame, f"Updated: {timestamp}", (50, frame_shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 1)
            
            return stats_frame
            
        except Exception as e:
            logger.error(f"創建統計畫面時出錯: {e}")
            # 返回簡單的錯誤畫面
            error_frame = np.zeros(frame_shape, dtype=np.uint8)
            cv2.putText(error_frame, "Stats Display Error", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return error_frame

    def _add_new_roi_region(self):
        """添加新的ROI區域"""
        logger.info("開始添加新的ROI區域 - 使用鼠標左鍵添加點，右鍵完成")
        self.current_region_points = []
        self.is_drawing_roi = True
        self.current_region_type = "person"
        self.current_region_closed = False

    def _add_new_bus_region(self):
        """添加新的公交車區域"""
        logger.info("開始添加新的公交車區域 - 使用鼠標左鍵添加點，右鍵完成")
        self.current_region_points = []
        self.is_drawing_roi = True
        self.current_region_type = "bus"
        self.current_region_closed = False

    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數用於繪製和編輯ROI區域 - 改進版"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 檢查是否在編輯模式
            if self.edit_mode and self.selected_region_index >= 0:
                self._handle_edit_mode_click(x, y)
                return

            # 左鍵點擊：添加點到當前區域
            if self.current_region_closed:
                logger.warning("當前區域已關閉，請按 'n' 開始新人頭區域或 'b' 開始新公車區域")
                return
            self.current_region_points.append([x, y])
            logger.info(f"添加ROI點到當前{self.current_region_type}區域: ({x}, {y}) - 點數: {len(self.current_region_points)}")

        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右鍵點擊：在編輯模式下刪除頂點，否則關閉當前區域
            if self.edit_mode and self.selected_region_index >= 0:
                self._handle_edit_mode_right_click(x, y)
                return

            if not self.current_region_closed and len(self.current_region_points) > 2:
                region_id = self._add_new_region()
                if region_id >= 0:
                    self.current_region_closed = True
                    region_type = self.current_region_type
                    logger.info(f"✅ {region_type}區域 {region_id + 1} 已關閉並自動保存")
                else:
                    logger.warning("區域點數不足，無法關閉")

        elif event == cv2.EVENT_MBUTTONDOWN:
            # 中鍵點擊：選擇/取消選擇區域（用於編輯）
            success = self._select_region_at_point(x, y)
            if success and not self.edit_mode:
                logger.info("提示：按 'e' 進入編輯模式來修改選中的區域")

        elif event == cv2.EVENT_LBUTTONDBLCLK:
            # 雙擊左鍵：進入編輯模式
            if not self.edit_mode:
                self._enter_edit_mode_at_point(x, y)

    def _handle_edit_mode_right_click(self, x, y):
        """處理編輯模式下的右鍵點擊 - 刪除頂點"""
        if self.selected_region_index < 0:
            return

        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                return
            region = self.bus_regions[self.selected_region_index]

        points = region['points']

        # 至少需要保留3個點
        if len(points) <= 3:
            logger.warning("區域至少需要3個頂點，無法刪除更多頂點")
            return

        # 找到最接近的頂點
        min_distance = float('inf')
        closest_index = -1

        for i, point in enumerate(points):
            distance = np.sqrt((x - point[0])**2 + (y - point[1])**2)
            if distance < min_distance:
                min_distance = distance
                closest_index = i

        # 如果點擊位置接近某個頂點，刪除該頂點
        if min_distance <= 15 and closest_index >= 0:
            deleted_point = points.pop(closest_index)
            logger.info(f"刪除頂點 {closest_index + 1}: ({deleted_point[0]}, {deleted_point[1]})")
            self._save_multi_roi()
        else:
            logger.info("右鍵點擊位置不在任何頂點附近，無法刪除")

    def _select_region_at_point(self, x, y):
        """選擇指定點所在的區域 - 改進的邊界檢測"""
        selection_tolerance = 25  # 增加選擇容差

        logger.info(f"🖱️ 中鍵點擊位置: ({x}, {y})")

        # 收集所有可能的候選區域
        candidates = []

        # 檢查人頭區域
        for i, region in enumerate(self.roi_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)

                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('person', i, region, 0))
                    continue

                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('person', i, region, distance))
                    continue

                # 額外檢查：檢查點是否接近任何邊線
                edge_distance = self._get_distance_to_polygon_edge(x, y, region['points'])
                if edge_distance <= selection_tolerance:
                    candidates.append(('person', i, region, edge_distance))

        # 檢查公車區域
        for i, region in enumerate(self.bus_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)

                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('bus', i, region, 0))
                    continue

                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('bus', i, region, distance))
                    continue

                # 額外檢查：檢查點是否接近任何邊線
                edge_distance = self._get_distance_to_polygon_edge(x, y, region['points'])
                if edge_distance <= selection_tolerance:
                    candidates.append(('bus', i, region, edge_distance))

        if candidates:
            # 按距離排序，優先選擇距離最近的
            candidates.sort(key=lambda x: x[3])
            region_type, region_index, region, distance = candidates[0]

            self.selected_region_index = region_index
            self.selected_region_type = region_type

            region_name = region.get('name', f'{region_type.title()}_Region_{region_index + 1}')
            distance_info = "(內部)" if distance == 0 else f"(邊界附近 {distance:.1f}px)"
            logger.info(f"✅ 選中{region_type}區域 {region_index + 1}: {region_name} {distance_info}")
            return True

        self.selected_region_index = -1
        self.selected_region_type = 'person'
        logger.info("❌ 未選中任何區域 - 請點擊區域內部或邊界附近")
        return False

    def _get_distance_to_polygon_edge(self, x, y, polygon_points):
        """計算點到多邊形邊線的最短距離"""
        if len(polygon_points) < 2:
            return float('inf')

        min_distance = float('inf')

        # 檢查每條邊線
        for i in range(len(polygon_points)):
            p1 = polygon_points[i]
            p2 = polygon_points[(i + 1) % len(polygon_points)]

            # 計算點到線段的距離
            distance = self._point_to_line_distance(x, y, p1[0], p1[1], p2[0], p2[1])
            min_distance = min(min_distance, distance)

        return min_distance

    def _point_to_line_distance(self, px, py, x1, y1, x2, y2):
        """計算點到線段的最短距離"""
        # 線段長度的平方
        line_length_sq = (x2 - x1) ** 2 + (y2 - y1) ** 2

        if line_length_sq == 0:
            # 線段退化為點
            return np.sqrt((px - x1) ** 2 + (py - y1) ** 2)

        # 計算投影參數 t
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_length_sq))

        # 投影點
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)

        # 返回距離
        return np.sqrt((px - proj_x) ** 2 + (py - proj_y) ** 2)

    def _select_region_for_edit(self, region_index):
        """選擇區域進行編輯（數字鍵快速選擇）"""
        logger.info(f"🔍 嘗試選擇區域 {region_index + 1}")
        
        # 先檢查人頭區域
        if region_index < len(self.roi_regions):
            self.selected_region_index = region_index
            self.selected_region_type = 'person'
            region_name = self.roi_regions[region_index].get('name', f'Person_Region_{region_index + 1}')
            logger.info(f"✅ 快速選中人頭區域 {region_index + 1}: {region_name}")
            return
        
        # 再檢查公車區域
        bus_index = region_index - len(self.roi_regions)
        if bus_index >= 0 and bus_index < len(self.bus_regions):
            self.selected_region_index = bus_index
            self.selected_region_type = 'bus'
            region_name = self.bus_regions[bus_index].get('name', f'Bus_Region_{bus_index + 1}')
            logger.info(f"✅ 快速選中公車區域 {bus_index + 1}: {region_name}")
            return
        
        # 沒有找到對應的區域
        total_regions = len(self.roi_regions) + len(self.bus_regions)
        logger.warning(f"❌ 區域 {region_index + 1} 不存在 (總共有 {total_regions} 個區域)")

    def _enter_edit_mode_at_point(self, x, y):
        """在指定點進入編輯模式"""
        self._select_region_at_point(x, y)
        if self.selected_region_index >= 0:
            self.edit_mode = True
            logger.info(f"進入編輯模式 - {self.selected_region_type}區域 {self.selected_region_index + 1}")
        else:
            logger.warning("未找到可編輯的區域")

    def _exit_edit_mode(self):
        """退出編輯模式"""
        if self.edit_mode:
            self.edit_mode = False
            logger.info("已退出編輯模式")

    def _add_new_region(self):
        """添加新的ROI區域到對應的列表中"""
        if len(self.current_region_points) < 3:
            logger.warning("區域點數不足（需要至少3個點）")
            return -1
        
        try:
            # 創建新區域
            new_region = {
                'points': self.current_region_points.copy(),
                'polygon': self.current_region_points.copy(),
                'name': f'{self.current_region_type.title()}_Region_{len(self.roi_regions if self.current_region_type == "person" else self.bus_regions) + 1}',
                'type': self.current_region_type,
                'closed': True,
                'id': len(self.roi_regions if self.current_region_type == "person" else self.bus_regions)
            }
            
            # 根據區域類型添加到對應列表
            if self.current_region_type == "person":
                self.roi_regions.append(new_region)
                region_id = len(self.roi_regions) - 1
                logger.info(f"✅ 新增人頭區域 {region_id + 1}: {len(self.current_region_points)} 個點")
            elif self.current_region_type == "bus":
                self.bus_regions.append(new_region)
                region_id = len(self.bus_regions) - 1
                logger.info(f"✅ 新增公車區域 {region_id + 1}: {len(self.current_region_points)} 個點")
            else:
                logger.error(f"未知的區域類型: {self.current_region_type}")
                return -1
            
            # 自動保存
            self._save_multi_roi()
            
            # 重置當前區域
            self.current_region_points = []
            self.is_drawing_roi = False
            
            return region_id
            
        except Exception as e:
            logger.error(f"添加新區域時出錯: {e}")
            return -1

    def _handle_edit_mode_click(self, x, y):
        """處理編輯模式下的點擊事件 - 完整的編輯功能"""
        if self.selected_region_index < 0:
            logger.warning("請先選擇一個區域進行編輯")
            return

        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                logger.error("選中的人頭區域索引超出範圍")
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                logger.error("選中的公車區域索引超出範圍")
                return
            region = self.bus_regions[self.selected_region_index]

        points = region['points']
        edit_threshold = 15  # 編輯點選擇閾值

        logger.info(f"編輯模式點擊: ({x}, {y}) - 編輯{self.selected_region_type}區域 {self.selected_region_index + 1}")

        # 檢查是否點擊了現有的頂點
        closest_point_index = -1
        min_distance = float('inf')

        for i, point in enumerate(points):
            distance = np.sqrt((x - point[0])**2 + (y - point[1])**2)
            if distance < min_distance:
                min_distance = distance
                closest_point_index = i

        # 如果點擊了現有頂點，移動該頂點
        if min_distance <= edit_threshold:
            points[closest_point_index] = [x, y]
            logger.info(f"移動頂點 {closest_point_index + 1} 到 ({x}, {y})")
            self._save_multi_roi()
            return

        # 檢查是否點擊了邊線，如果是則在該位置插入新頂點
        insertion_index = self._find_edge_insertion_point(x, y, points, edit_threshold)
        if insertion_index >= 0:
            points.insert(insertion_index + 1, [x, y])
            logger.info(f"在邊線上插入新頂點 {insertion_index + 2}: ({x}, {y})")
            self._save_multi_roi()
            return

        logger.info("點擊位置不在任何頂點或邊線附近")

    def _find_edge_insertion_point(self, x, y, points, threshold):
        """找到最適合插入新頂點的邊線位置"""
        if len(points) < 2:
            return -1

        min_distance = float('inf')
        best_edge_index = -1

        for i in range(len(points)):
            p1 = points[i]
            p2 = points[(i + 1) % len(points)]

            # 計算點到線段的距離
            distance = self._point_to_line_distance(x, y, p1[0], p1[1], p2[0], p2[1])

            if distance < min_distance and distance <= threshold:
                min_distance = distance
                best_edge_index = i

        return best_edge_index if min_distance <= threshold else -1

    def _show_help(self):
        """顯示幫助信息"""
        help_text = """
=== GPU優化頭部檢測系統 (WebSocket版本) - 操作說明 ===

基本操作:
  q     - 退出程序
  v     - 切換WebSocket圖片/檢測統計顯示
  d     - 切換顯示模式 (顯示/隱藏視頻窗口)
  h     - 顯示此幫助信息

ROI區域操作:
  n     - 添加新的人頭檢測區域
  b     - 添加新的公交車檢測區域
  左鍵  - 添加ROI區域頂點
  右鍵  - 完成當前ROI區域繪製
  s     - 保存所有ROI區域到文件
  c     - 清除當前正在繪製的區域
  r     - 重置所有ROI區域
  t     - 切換區域類型 (人頭/公車)

區域選擇與編輯:
  中鍵     - 選擇區域 (點擊區域內部或邊界附近)
  雙擊左鍵 - 快速進入編輯模式
  e        - 切換編輯模式
  1-9      - 快速選擇區域進行編輯

編輯模式操作 (需先選擇區域):
  左鍵  - 移動頂點或在邊線上插入新頂點
  右鍵  - 刪除最接近的頂點 (至少保留3個頂點)

區域類型與顏色:
  - 人頭檢測區域: 紅色邊框，選中時變為黃色
  - 公車檢測區域: 藍色邊框，選中時變為青色
  - 選中區域會顯示頂點和 [選中] 標記

改進的邊界檢測:
  - 增強的區域選擇算法
  - 更精確的邊界距離計算
  - 支持邊線附近的點擊選擇

WebSocket連接:
  - 默認連接: ws://*************:8004
  - 接收base64編碼的JPEG圖像
  - 自動重連機制

性能優化:
  - GPU加速推理 (如果可用)
  - 硬體編碼 (如果啟用)
  - 批次處理優化
  - 動態顯示切換

注意: ROI區域會自動保存到 multi_roi_regions.json
========================================================
        """
        logger.info(help_text)

    def _clear_current_region(self):
        """清除當前正在繪製的區域"""
        if len(self.current_region_points) > 0:
            logger.info(f"清除當前區域 ({len(self.current_region_points)} 個點)")
            self.current_region_points = []
            self.current_region_closed = False
        else:
            logger.info("沒有正在繪製的區域")

    def _reset_all_regions(self):
        """重置所有ROI區域"""
        logger.info("重置所有ROI區域...")
        self.roi_regions = []
        self.bus_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        self.edit_mode = False

        # 刪除保存的文件
        if os.path.exists(self.roi_file):
            try:
                os.remove(self.roi_file)
                logger.info("✅ 已刪除ROI區域文件")
            except Exception as e:
                logger.error(f"刪除ROI區域文件失敗: {e}")

        logger.info("✅ 所有ROI區域已重置")

    def _toggle_edit_mode(self):
        """切換編輯模式"""
        self.edit_mode = not self.edit_mode
        if self.edit_mode:
            logger.info("✏️ 編輯模式已啟用 - 使用數字鍵1-9選擇要編輯的區域")
        else:
            logger.info("✏️ 編輯模式已停用")
            self.selected_region_index = -1
            self.edit_point_index = -1
            self.drag_mode = False

    def _toggle_region_type(self):
        """切換區域類型"""
        if self.current_region_type == 'person':
            self.current_region_type = 'bus'
            logger.info("🚌 切換到公車檢測區域模式 (藍色)")
        else:
            self.current_region_type = 'person'
            logger.info("👤 切換到人頭檢測區域模式 (紅色)")

    def _select_region_for_edit(self, region_index):
        """選擇區域進行編輯"""
        if not self.edit_mode:
            logger.info("請先按 'e' 啟用編輯模式")
            return

        total_regions = self.roi_regions + self.bus_regions
        if 0 <= region_index < len(total_regions):
            self.selected_region_index = region_index
            region = total_regions[region_index]
            logger.info(f"✏️ 選中區域 {region_index + 1}: {region.get('name', 'Unknown')}")
        else:
            logger.info(f"區域索引 {region_index + 1} 超出範圍 (總共 {len(total_regions)} 個區域)")

    def _update_statistics(self, detection_results):
        """更新統計數據"""
        # 更新總計數
        self.total_statistics['total_count'] = detection_results['total_count']

        # 更新區域統計
        region_counts = []
        for region_result in detection_results['region_results']:
            region_id = region_result['region_id']
            count = region_result['count']
            region_counts.append({
                'region_id': region_id,
                'region_name': region_result['region_name'],
                'count': count
            })

            # 更新區域統計歷史
            if region_id not in self.region_statistics:
                self.region_statistics[region_id] = {
                    'name': region_result['region_name'],
                    'history': [],
                    'max_count': 0,
                    'total_detections': 0
                }

            self.region_statistics[region_id]['history'].append(count)
            self.region_statistics[region_id]['max_count'] = max(
                self.region_statistics[region_id]['max_count'], count
            )
            self.region_statistics[region_id]['total_detections'] += count

        self.total_statistics['region_counts'] = region_counts

        # 更新公車統計
        for bus_result in detection_results['bus_results']:
            region_id = bus_result['region_id']
            count = bus_result['count']

            if region_id not in self.bus_statistics:
                self.bus_statistics[region_id] = {
                    'name': bus_result['region_name'],
                    'history': [],
                    'max_count': 0,
                    'total_detections': 0
                }

            self.bus_statistics[region_id]['history'].append(count)
            self.bus_statistics[region_id]['max_count'] = max(
                self.bus_statistics[region_id]['max_count'], count
            )
            self.bus_statistics[region_id]['total_detections'] += count

        # 保持歷史記錄在合理範圍內
        max_history = 1000
        for stats in self.region_statistics.values():
            if len(stats['history']) > max_history:
                stats['history'] = stats['history'][-max_history:]

        for stats in self.bus_statistics.values():
            if len(stats['history']) > max_history:
                stats['history'] = stats['history'][-max_history:]

    def _log_performance_stats(self, frame_count, start_time):
        """記錄性能統計"""
        elapsed_time = time.time() - start_time
        avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0

        if len(self.frame_times) > 0:
            avg_detection_time = sum(self.frame_times[-100:]) / min(len(self.frame_times), 100)
            logger.info(f"📊 幀數: {frame_count}, 平均FPS: {avg_fps:.1f}, 平均檢測時間: {avg_detection_time:.3f}s")

        if self.detector.device == 'cuda' and len(self.gpu_memory_usage) > 0:
            avg_gpu_memory = sum(self.gpu_memory_usage[-100:]) / min(len(self.gpu_memory_usage), 100)
            logger.info(f"🎮 GPU記憶體使用: {avg_gpu_memory:.0f}MB")

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.15, device='auto', batch_size=1):
        """
        GPU优化的头部检测器
        
        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值
            device: 运行设备 ('auto', 'cuda', 'cpu')
            batch_size: 批处理大小（GPU优化）
        """
        self.conf_threshold = conf_threshold
        self.requested_device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # COCO類別定義
        self.coco_classes = {
            0: 'person',
            5: 'bus',
            25: 'umbrella'
        }
        
        # 自動偵測並設置最佳設備
        self.device = self._auto_detect_device()
        
        # 设置GPU优化参数
        self._setup_gpu_optimization()
        
        # 加载模型
        self._load_model()
        
        # 加载车辆检测专用模型
        self._load_vehicle_model()
        
        # 記錄初始設備信息
        self._log_device_info()
    
    def _auto_detect_device(self):
        """自動偵測並選擇最佳運行設備（GPU優先）"""
        if self.requested_device == 'cpu':
            logger.info("🖥️ 用戶指定使用CPU模式")
            return 'cpu'
        elif self.requested_device == 'cuda':
            if torch.cuda.is_available():
                logger.info("🚀 用戶指定使用CUDA GPU模式")
                return 'cuda'
            else:
                logger.warning("⚠️ 用戶指定CUDA但GPU不可用，自動切換到CPU模式")
                return 'cpu'
        else:  # self.requested_device == 'auto'
            return self._detect_best_device()
    
    def _detect_best_device(self):
        """智能偵測最佳可用設備"""
        logger.info("🔍 自動偵測最佳運行設備...")
        
        # 優先檢查CUDA GPU
        if torch.cuda.is_available():
            try:
                # 檢查GPU記憶體和性能
                gpu_count = torch.cuda.device_count()
                current_gpu = torch.cuda.current_device()
                gpu_name = torch.cuda.get_device_name(current_gpu)
                gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
                
                # 檢查GPU是否有足夠記憶體（至少2GB）
                if gpu_memory >= 2.0:
                    # 測試GPU性能
                    if self._test_gpu_performance():
                        logger.info(f"✅ 自動選擇GPU: {gpu_name} ({gpu_memory:.1f}GB)")
                        logger.info(f"   - GPU數量: {gpu_count}")
                        logger.info(f"   - CUDA版本: {torch.version.cuda}")
                        return 'cuda'
                    else:
                        logger.warning("⚠️ GPU性能測試失敗，切換到CPU模式")
                        return 'cpu'
                else:
                    logger.warning(f"⚠️ GPU記憶體不足 ({gpu_memory:.1f}GB < 2.0GB)，切換到CPU模式")
                    return 'cpu'
                    
            except Exception as e:
                logger.warning(f"⚠️ GPU檢測過程中出現錯誤: {e}，切換到CPU模式")
                return 'cpu'
        else:
            logger.info("📱 CUDA不可用，使用CPU模式")
            return 'cpu'
    
    def _test_gpu_performance(self):
        """測試GPU性能是否適合運行模型"""
        try:
            logger.info("🧪 測試GPU性能...")
            
            # 創建測試張量
            test_tensor = torch.randn(1, 3, 640, 640).cuda()
            
            # 測試基本運算
            start_time = time.time()
            for _ in range(5):
                result = test_tensor * 2.0 + 1.0
                torch.cuda.synchronize()
            end_time = time.time()
            
            test_time = end_time - start_time
            logger.info(f"   GPU基本運算測試: {test_time:.3f}秒")
            
            # 清理測試張量
            del test_tensor, result
            torch.cuda.empty_cache()
            
            # 如果測試時間合理（小於1秒），認為GPU性能可接受
            if test_time < 1.0:
                logger.info("   ✅ GPU性能測試通過")
                return True
            else:
                logger.warning(f"   ⚠️ GPU性能較慢 ({test_time:.3f}s)，可能不適合實時處理")
                return False
                
        except Exception as e:
            logger.warning(f"   ❌ GPU性能測試失敗: {e}")
            return False
        
    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if self.device == 'cuda':
            try:
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                torch.cuda.empty_cache()
                
                gpu_count = torch.cuda.device_count()
                current_gpu = torch.cuda.current_device()
                gpu_name = torch.cuda.get_device_name(current_gpu)
                gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
                gpu_memory_free = torch.cuda.get_device_properties(current_gpu).total_memory - torch.cuda.memory_allocated()
                gpu_memory_free_gb = gpu_memory_free / 1024**3
                
                logger.info(f"🚀 GPU優化設置:")
                logger.info(f"  - GPU數量: {gpu_count}")
                logger.info(f"  - 當前GPU: {current_gpu} ({gpu_name})")
                logger.info(f"  - GPU總記憶體: {gpu_memory:.1f} GB")
                logger.info(f"  - GPU可用記憶體: {gpu_memory_free_gb:.1f} GB")
                logger.info(f"  - CUDA版本: {torch.version.cuda}")
                logger.info(f"  - cuDNN啟用: {torch.backends.cudnn.enabled}")
                logger.info(f"  - cuDNN基準: {torch.backends.cudnn.benchmark}")
                
                # 根據GPU記憶體調整批次大小
                if gpu_memory < 4.0:
                    self.batch_size = 1
                    logger.info(f"  - 批次大小調整為: {self.batch_size} (低記憶體GPU)")
                elif gpu_memory < 8.0:
                    self.batch_size = min(self.batch_size, 2)
                    logger.info(f"  - 批次大小調整為: {self.batch_size} (中等記憶體GPU)")
                else:
                    logger.info(f"  - 批次大小: {self.batch_size} (高記憶體GPU)")
                
            except Exception as e:
                logger.error(f"❌ GPU優化設置失敗: {e}")
                logger.warning("🔄 自動切換到CPU模式")
                self.device = 'cpu'
                self._setup_cpu_optimization()
        else:
            self._setup_cpu_optimization()
    
    def _setup_cpu_optimization(self):
        """設置CPU優化參數"""
        logger.info("🖥️ CPU優化設置:")
        logger.info(f"  - 使用設備: CPU")
        logger.info(f"  - 批次大小: {self.batch_size}")
        
        # CPU模式下的優化設置
        torch.set_num_threads(4)  # 限制CPU線程數以避免過度佔用
        logger.info(f"  - CPU線程數: {torch.get_num_threads()}")
        
        # 禁用一些GPU專用的優化
        if hasattr(torch.backends, 'cudnn'):
            torch.backends.cudnn.enabled = False
    
    def _log_device_info(self):
        """記錄當前設備信息"""
        logger.info("=" * 50)
        logger.info("🔧 設備配置摘要:")
        logger.info(f"  - 請求設備: {self.requested_device}")
        logger.info(f"  - 實際使用: {self.device.upper()}")
        logger.info(f"  - 模型類型: {self.model_type}")
        logger.info(f"  - 批次大小: {self.batch_size}")
        logger.info(f"  - 置信度閾值: {self.conf_threshold}")
        
        if self.device == 'cuda':
            gpu_name = torch.cuda.get_device_name(torch.cuda.current_device())
            gpu_memory = torch.cuda.get_device_properties(torch.cuda.current_device()).total_memory / 1024**3
            logger.info(f"  - GPU型號: {gpu_name}")
            logger.info(f"  - GPU記憶體: {gpu_memory:.1f}GB")
        
        logger.info("=" * 50)
    
    def switch_device(self, new_device):
        """運行時切換設備"""
        if new_device == self.device:
            logger.info(f"✅ 已經在使用 {new_device.upper()} 模式")
            return True
            
        logger.info(f"🔄 嘗試從 {self.device.upper()} 切換到 {new_device.upper()}...")
        
        # 檢查目標設備是否可用
        if new_device == 'cuda' and not torch.cuda.is_available():
            logger.error("❌ CUDA不可用，無法切換到GPU模式")
            return False
        
        try:
            # 保存當前配置
            old_device = self.device
            self.device = new_device
            
            # 重新設置優化參數
            self._setup_gpu_optimization()
            
            # 重新載入模型到新設備
            logger.info("🔄 重新載入模型到新設備...")
            self.model.to(self.device)
            
            # 如果切換到GPU，啟用半精度
            if self.device == 'cuda':
                self.model.half()
                self._warmup_gpu()
            else:
                # 切換到CPU時，轉換為全精度
                self.model.float()
            
            logger.info(f"✅ 成功切換到 {new_device.upper()} 模式")
            self._log_device_info()
            return True
            
        except Exception as e:
            logger.error(f"❌ 設備切換失敗: {e}")
            # 恢復到原設備
            self.device = old_device
            logger.info(f"🔄 已恢復到 {old_device.upper()} 模式")
            return False
    
    def _load_model(self):
        """加载并优化模型"""
        try:
            logger.info("加载YOLOv5模型...")
            logger.info(f"PyTorch 版本: {torch.__version__}")
            logger.info(f"修復狀態: {'已修復' if PYTORCH_FIX_AVAILABLE else '未修復'}")
            
            if self.model_type == 'custom':
                try:
                    logger.info("嘗試加載自定義頭部檢測模型...")
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True,
                                               trust_repo=True, verbose=False)
                    logger.info("✅ 加载专门的头部检测模型成功")
                except Exception as e:
                    logger.warning(f"未找到头部检测模型: {e}")
                    logger.info("🔄 切換到人體檢測模型...")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', 
                                               pretrained=True, force_reload=True,
                                               trust_repo=True, verbose=False)
                    self.model_type = 'person'
                    logger.info("✅ 人體檢測模型加載成功")
            else:
                logger.info("加載標準 YOLOv5m 模型...")
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', 
                                           pretrained=True, force_reload=True,
                                           trust_repo=True, verbose=False)
                logger.info("✅ YOLOv5m 模型加載成功")
            
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.5
            self.model.max_det = 1000
            
            if self.device == 'cuda':
                self.model.half()
                logger.info("启用FP16半精度推理")
                self._warmup_gpu()
            
            logger.info(f"模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU预热以获得稳定性能"""
        logger.info("GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU预热完成")
    
    def _load_vehicle_model(self):
        """加载车辆检测专用模型 (YOLOv5n)"""
        try:
            logger.info("加载车辆检测专用模型 (YOLOv5n)...")
            
            # 加载轻量级YOLOv5n模型用于车辆检测
            self.vehicle_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.vehicle_model.to(self.device)
            self.vehicle_model.conf = 0.3  # 车辆检测使用更低的置信度阈值
            self.vehicle_model.iou = 0.45
            self.vehicle_model.max_det = 1000
            
            if self.device == 'cuda':
                self.vehicle_model.half()
                logger.info("车辆检测模型启用FP16半精度推理")
                # GPU预热车辆模型
                self._warmup_vehicle_gpu()
            
            logger.info(f"车辆检测模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"车辆检测模型加载失败: {e}")
            logger.warning("将使用主模型进行车辆检测")
            self.vehicle_model = self.model
    
    def _warmup_vehicle_gpu(self):
        """车辆检测模型GPU预热"""
        logger.info("车辆检测模型GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(2):
                _ = self.vehicle_model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("车辆检测模型GPU预热完成")
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """从人体检测框中提取头部区域"""
        x1, y1, x2, y2 = person_bbox
        person_height = y2 - y1
        person_width = x2 - x1
        head_height = int(person_height * head_ratio)
        head_width_margin = int(person_width * 0.1)
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads_batch(self, frames, roi_regions=None, bus_regions=None):
        """批量檢測頭部和公車（GPU優化，支持多區域）"""
        if not isinstance(frames, list):
            frames = [frames]

        results_list = []

        try:
            # 人頭檢測使用主模型
            with torch.no_grad():
                if self.device == 'cuda':
                    torch.cuda.synchronize()
                head_results = self.model(frames)
                if self.device == 'cuda':
                    torch.cuda.synchronize()
            
            # 車輛檢測使用專用模型
            vehicle_results = None
            if bus_regions and len(bus_regions) > 0:
                with torch.no_grad():
                    if self.device == 'cuda':
                        torch.cuda.synchronize()
                    vehicle_results = self.vehicle_model(frames)
                    if self.device == 'cuda':
                        torch.cuda.synchronize()

            for i, frame in enumerate(frames):
                head_frame_results = head_results if len(frames) == 1 else [head_results[i]]
                vehicle_frame_results = vehicle_results if vehicle_results is None or len(frames) == 1 else [vehicle_results[i]]
                
                multi_region_results = self._process_multi_region_frame(
                    frame, head_frame_results, vehicle_frame_results, roi_regions, bus_regions, frame_count=i)
                results_list.append(multi_region_results)

            return results_list

        except Exception as e:
            logger.error(f"批量檢測過程中出錯: {e}")
            return [{'total_count': 0, 'region_results': [], 'bus_count': 0, 'bus_results': [], 'annotated_frame': frame} for frame in frames]
    
    def _process_multi_region_frame(self, frame, head_results, vehicle_results, roi_regions=None, bus_regions=None, frame_count=0):
        """處理多區域檢測結果（包含人頭和公車）"""
        try:
            # 處理人頭檢測結果
            head_detections = head_results.pandas().xyxy[0]
            all_head_bboxes = []
            annotated_frame = frame.copy()

            # 獲取人頭檢測結果
            if self.model_type == 'custom':
                target_head_detections = head_detections[head_detections['name'].isin(['head', 'person'])]
            else:
                target_head_detections = head_detections[head_detections['class'].isin([0, 25])]

            for _, detection in target_head_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else:
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                all_head_bboxes.append(bbox + [confidence])

            # 處理車輛檢測結果
            all_bus_bboxes = []
            if vehicle_results is not None:
                vehicle_detections = vehicle_results.pandas().xyxy[0]
                
                # 車輛檢測調試信息
                all_vehicle_classes = vehicle_detections['class'].unique() if len(vehicle_detections) > 0 else []
                if frame_count % 30 == 1:  # 每30幀顯示一次調試信息
                    logger.info(f"幀 {frame_count}: 車輛模型檢測到的所有類別: {sorted(all_vehicle_classes)}")
                    logger.info(f"車輛檢測總數量: {len(vehicle_detections)}")
                
                # 專門檢測公車（COCO類別5）
                bus_class = 5  # 只檢測公車
                
                all_detections = vehicle_detections[vehicle_detections['class'] == bus_class]
                if len(all_detections) > 0 and frame_count % 30 == 1:
                    logger.info(f"YOLOv5s檢測到公車候選: {len(all_detections)} 個")
                    for _, det in all_detections.iterrows():
                        logger.info(f"YOLOv5s公車候選: 信心值 {det['confidence']:.3f}")
                
                # 專門檢測公車
                bus_detections = vehicle_detections[vehicle_detections['class'] == bus_class]
                
                # 先不過濾置信度，看看有沒有任何公車檢測
                if len(bus_detections) > 0:
                    logger.info(f"YOLOv5s檢測到公車數量: {len(bus_detections)}")
                    for _, det in bus_detections.iterrows():
                        logger.info(f"YOLOv5s公車: 信心值 {det['confidence']:.3f}")
                
                # 過濾置信度過低的檢測 - 極低閾值測試
                bus_detections = bus_detections[bus_detections['confidence'] >= 0.3]  # 更低閾值
                if len(bus_detections) > 0:
                    logger.info(f"YOLOv5s過濾後公車數量: {len(bus_detections)} (置信度 >= 0.3)")
                
                for _, detection in bus_detections.iterrows():
                    confidence = detection['confidence']
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    all_bus_bboxes.append(bbox + [confidence])
                    logger.info(f"YOLOv5s公車檢測: 位置({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}) 信心值:{confidence:.3f}")
            else:
                if frame_count % 30 == 1:
                    logger.info("未啟用車輛檢測模型（無車輛ROI區域）")

            # 處理人頭ROI區域
            region_results = []
            total_count = 0

            if roi_regions and len(roi_regions) > 0:
                for region in roi_regions:
                    if region.get('closed', True) and len(region['points']) > 2:
                        region_bboxes = self._filter_detections_by_region(all_head_bboxes, region['points'])
                        region_count = len(region_bboxes)
                        total_count += region_count

                        region_result = {
                            'region_id': region['id'],
                            'region_name': region['name'],
                            'region_type': 'person',
                            'count': region_count,
                            'bboxes': region_bboxes,
                            'points': region['points']
                        }
                        region_results.append(region_result)
            else:
                # 如果沒有ROI區域，使用所有檢測結果
                total_count = len(all_head_bboxes)
                region_results.append({
                    'region_id': -1,
                    'region_name': 'Full_Frame_Person',
                    'region_type': 'person',
                    'count': total_count,
                    'bboxes': all_head_bboxes,
                    'points': []
                })

            # 處理公車ROI區域
            bus_results = []
            bus_total_count = 0

            if bus_regions and len(bus_regions) > 0:
                logger.info(f"處理 {len(bus_regions)} 個公車區域，檢測到 {len(all_bus_bboxes)} 個車輛")
                for region in bus_regions:
                    if region.get('closed', True) and len(region['points']) > 2:
                        region_bboxes = self._filter_detections_by_region(all_bus_bboxes, region['points'])
                        region_count = len(region_bboxes)
                        bus_total_count += region_count

                        bus_result = {
                            'region_id': region['id'],
                            'region_name': region['name'],
                            'region_type': 'bus',
                            'count': region_count,
                            'bboxes': region_bboxes,
                            'points': region['points']
                        }
                        bus_results.append(bus_result)
                        logger.info(f"公車區域 {region['name']}: {region_count} 輛車")
            else:
                # 如果沒有公車ROI區域，使用所有公車檢測結果
                bus_total_count = len(all_bus_bboxes)
                if bus_total_count > 0:
                    logger.info(f"無公車ROI區域，全幀檢測到 {bus_total_count} 輛車")
                    bus_results.append({
                        'region_id': -1,
                        'region_name': 'Full_Frame_Vehicle',
                        'region_type': 'bus',
                        'count': bus_total_count,
                        'bboxes': all_bus_bboxes,
                        'points': []
                    })

            # 繪製檢測結果
            annotated_frame = self._draw_multi_region_results(annotated_frame, region_results, total_count, bus_results, bus_total_count)

            return {
                'total_count': total_count,
                'region_results': region_results,
                'bus_count': bus_total_count,
                'bus_results': bus_results,
                'annotated_frame': annotated_frame
            }

        except Exception as e:
            logger.error(f"處理多區域檢測結果時出錯: {e}")
            return {
                'total_count': 0,
                'region_results': [],
                'bus_count': 0,
                'bus_results': [],
                'annotated_frame': frame
            }

    def _filter_detections_by_region(self, head_bboxes, region_points):
        """根據區域過濾檢測結果"""
        filtered_bboxes = []
        roi_np = np.array(region_points, dtype=np.int32)

        for bbox in head_bboxes:
            x1, y1, x2, y2, confidence = bbox
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                filtered_bboxes.append(bbox)

        return filtered_bboxes

    def _draw_multi_region_results(self, frame, region_results, total_count, bus_results=None, bus_total_count=0):
        """繪製多區域檢測結果（包含人頭和公車）"""
        # 定義不同區域的顏色
        person_colors = [
            (0, 0, 255),    # 紅色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (0, 255, 255),  # 黃色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
        
        # 公車使用不同的顏色系列（藍色系列，更粗的線條）
        bus_colors = [
            (255, 0, 0),    # 藍色
            (255, 100, 0),  # 深藍色
            (255, 150, 0),  # 中藍色
            (255, 200, 0),  # 淺藍色
            (255, 50, 50),  # 藍紫色
            (255, 0, 100),  # 深藍紫色
        ]

        global_head_id = 1
        global_bus_id = 1

        # 繪製人頭檢測結果
        for i, region_result in enumerate(region_results):
            color = person_colors[i % len(person_colors)]

            # 繪製該區域的檢測框（更小的正方形框）
            for bbox in region_result['bboxes']:
                x1, y1, x2, y2, confidence = bbox
                
                # 計算中心點
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                
                # 創建小正方形框（15x15像素）
                box_size = 15
                half_size = box_size // 2
                x1_small = center_x - half_size
                y1_small = center_y - half_size
                x2_small = center_x + half_size
                y2_small = center_y + half_size
                
                cv2.rectangle(frame, (x1_small, y1_small), (x2_small, y2_small), color, 1)

                # ID 標籤 - 更小的字體和標籤框
                label = f'P{global_head_id}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.25, 1)[0]
                cv2.rectangle(frame, (x1_small, y1_small - label_size[1] - 2),
                            (x1_small + label_size[0] + 2, y1_small), color, -1)
                cv2.putText(frame, label, (x1_small + 1, y1_small - 1),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.25, (255, 255, 255), 1)

                # 信心值 - 更小字體
                conf_text = f'{confidence:.2f}'
                cv2.putText(frame, conf_text, (x1_small + 1, y1_small - label_size[1] - 4),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.2, (255, 255, 255), 1)

                # 中心點 - 更小
                cv2.circle(frame, (center_x, center_y), 1, color, -1)

                global_head_id += 1

        # 繪製公車檢測結果
        if bus_results:
            for i, bus_result in enumerate(bus_results):
                color = bus_colors[i % len(bus_colors)]

                # 繪製公車檢測框（更小的正方形框）
                for bbox in bus_result['bboxes']:
                    x1, y1, x2, y2, confidence = bbox
                    
                    # 計算中心點
                    center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                    
                    # 創建小正方形框（20x20像素，比人頭稍大）
                    box_size = 20
                    half_size = box_size // 2
                    x1_small = center_x - half_size
                    y1_small = center_y - half_size
                    x2_small = center_x + half_size
                    y2_small = center_y + half_size
                    
                    cv2.rectangle(frame, (x1_small, y1_small), (x2_small, y2_small), color, 2)

                    # ID 標籤 - 顯示公車類型
                    label = f'B{global_bus_id}'  # B for Bus
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.3, 1)[0]
                    cv2.rectangle(frame, (x1_small, y1_small - label_size[1] - 3),
                                (x1_small + label_size[0] + 3, y1_small), color, -1)
                    cv2.putText(frame, label, (x1_small + 1, y1_small - 1),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

                    # 信心值 - 更小字體
                    conf_text = f'{confidence:.2f}'
                    cv2.putText(frame, conf_text, (x1_small + 1, y1_small - label_size[1] - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.25, (255, 255, 255), 1)

                    # 中心點 - 更小
                    cv2.circle(frame, (center_x, center_y), 2, color, -1)

                    global_bus_id += 1

        # 顯示總計數（左上角）
        total_text = f'Person Count: {total_count}'
        cv2.putText(frame, total_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
        
        # 顯示公車總計數
        if bus_total_count > 0:
            bus_text = f'Bus Count: {bus_total_count}'
            cv2.putText(frame, bus_text, (10, 65),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 3)
            y_offset = 100
        else:
            y_offset = 70

        # 顯示各人頭區域計數
        for region_result in region_results:
            if region_result['region_id'] >= 0:  # 跳過全幀結果
                region_text = f"{region_result['region_name']}: {region_result['count']}"
                cv2.putText(frame, region_text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
                y_offset += 25

        # 顯示各公車區域計數
        if bus_results:
            for bus_result in bus_results:
                if bus_result['region_id'] >= 0:  # 跳過全幀結果
                    bus_region_text = f"{bus_result['region_name']}: {bus_result['count']} buses"
                    cv2.putText(frame, bus_region_text, (10, y_offset),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    y_offset += 25

        # 模式和GPU信息
        mode_text = f'Mode: {self.model_type.upper()} + YOLOv5s (GPU) - Multi-Region + Bus'
        cv2.putText(frame, mode_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)

        if self.device == 'cuda':
            gpu_memory = torch.cuda.memory_allocated() / 1024**2
            gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
            cv2.putText(frame, gpu_text, (10, y_offset + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 時間戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cv2.putText(frame, timestamp, (10, frame.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return frame

    def _process_single_frame_result(self, frame, results, roi_points=None):
        """处理单帧检测结果"""
        try:
            detections = results.pandas().xyxy[0]
            head_bboxes = []
            annotated_frame = frame.copy()
            
            if self.model_type == 'custom':
                target_detections = detections[detections['name'].isin(['head', 'person'])]
            else:
                # 0: person, 25: umbrella
                target_detections = detections[detections['class'].isin([0, 25])]

            for _, detection in target_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else: # Person or Umbrella
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                head_bboxes.append(bbox + [confidence])

            # 如果定义了ROI，则过滤检测结果
            if roi_points and len(roi_points) > 2:
                filtered_bboxes = []
                roi_np = np.array(roi_points, dtype=np.int32)
                for bbox in head_bboxes:
                    x1, y1, x2, y2, _ = bbox
                    center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                    if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                        filtered_bboxes.append(bbox)
                head_bboxes = filtered_bboxes

            # 绘制检测结果
            head_count = len(head_bboxes)
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
                label = f'{i+1}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                # ID 標籤底色
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6), 
                            (x1 + label_size[0] + 4, y1), (0, 0, 255), -1)
                # ID 文字
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                # 信心值（白色小字）
                conf_text = f'{confidence:.2f}'
                conf_size = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, 0.35, 1)[0]
                conf_y = y1 - label_size[1] - 8  # 再往上
                cv2.putText(annotated_frame, conf_text, (x1 + 2, conf_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, (255, 0, 0), -1)
            
            count_text = f'Person Count: {head_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            mode_text = f'Mode: {self.model_type.upper()} (GPU)'
            cv2.putText(annotated_frame, mode_text, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            if self.device == 'cuda':
                gpu_memory = torch.cuda.memory_allocated() / 1024**2
                gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
                cv2.putText(annotated_frame, gpu_text, (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"处理帧结果时出错: {e}")
            return 0, [], frame

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='gpu_head_detection.mp4', detection_interval=1, 
                 video_quality='medium', target_fps=None, target_resolution=None, use_hardware_encoding=True, use_hardware_decoding=True):
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        self.frame_times = []
        self.gpu_memory_usage = []
        
        # 新增：動態顯示控制
        self.display_enabled = True  # 預設啟用顯示
        self.window_created = False  # 追蹤視窗是否已創建
        self.last_display_toggle_time = 0  # 防止快速切換
        
        # 視頻優化設定
        self.video_quality = video_quality
        self.target_fps = target_fps
        self.target_resolution = target_resolution
        self.use_hardware_encoding = use_hardware_encoding
        self.use_hardware_decoding = use_hardware_decoding
        self.original_fps = None
        self.original_width = None
        self.original_height = None
        
        # 硬體編碼能力檢測
        self.hardware_capabilities = detect_hardware_encoding_capability() if use_hardware_encoding else None

        # 檢測間隔控制
        self.detection_interval = detection_interval
        self.last_detection_result = None  # 緩存上次檢測結果
        self.detection_frame_count = 0  # 檢測幀計數器

        # 多區域 ROI 设置
        self.roi_file = "multi_roi_regions.json"
        self.roi_regions = self._load_multi_roi()  # 存儲多個區域
        self.current_region_points = []  # 當前正在繪製的區域點
        self.current_region_closed = False  # 當前區域是否已關閉
        self.selected_region_index = -1  # 當前選中的區域索引（用於編輯）
        self.selected_region_type = 'person'  # 當前選中區域的類型
        self.window_name = 'GPU_Head_Detection'
        
        # 編輯模式設置
        self.edit_mode = False  # 是否處於編輯模式
        self.edit_point_index = -1  # 正在編輯的點索引
        self.drag_mode = False  # 是否處於拖拽模式
        self.edit_threshold = 10  # 點選擇的閾值（像素）
        
        # 公車檢測區域設置
        self.current_region_type = 'person'  # 當前繪製的區域類型：'person' 或 'bus'
        # self.bus_regions 已在 _load_multi_roi() 中初始化，不要重複初始化
        self.bus_statistics = {}  # 公車統計數據

        # 統計數據
        self.region_statistics = {}  # 每個區域的統計數據
        self.total_statistics = {
            'total_count': 0,
            'region_counts': [],
            'detection_history': []
        }

        logger.info(f"檢測間隔設置為: 每 {self.detection_interval} 幀檢測一次")
        
        # 顯示視頻優化設定
        self._log_video_optimization_settings()
        
        # 啟動時顯示載入的區域信息
        if len(self.roi_regions) > 0 or len(self.bus_regions) > 0:
            logger.info(f"✅ 成功載入 {len(self.roi_regions)} 個人頭檢測區域和 {len(self.bus_regions)} 個公車檢測區域")
            for i, region in enumerate(self.roi_regions):
                logger.info(f"   人頭區域 {i+1}: {region.get('name', f'Person_Region_{i+1}')} ({len(region['points'])} 個點)")
            for i, region in enumerate(self.bus_regions):
                logger.info(f"   公車區域 {i+1}: {region.get('name', f'Bus_Region_{i+1}')} ({len(region['points'])} 個點)")
        else:
            logger.info("⚠️  未找到已保存的檢測區域，請手動繪製ROI區域")

    def _log_video_optimization_settings(self):
        """顯示視頻優化設定"""
        if self.save_video:
            logger.info("=== 視頻優化設定 ===")
            logger.info(f"品質設定: {self.video_quality}")
            logger.info(f"硬體編碼: {'啟用' if self.use_hardware_encoding else '停用'}")
            if self.hardware_capabilities:
                logger.info(f"FFmpeg 可用: {self.hardware_capabilities['ffmpeg_available']}")
                if self.hardware_capabilities['ffmpeg_available']:
                    logger.info(f"建議編碼器: {self.hardware_capabilities['recommended_codec']}")
                    if self.hardware_capabilities['nvidia_nvenc']:
                        logger.info("  ✅ NVIDIA 硬體編碼支援")
                    if self.hardware_capabilities['intel_qsv']:
                        logger.info("  ✅ Intel Quick Sync 支援")
                    if self.hardware_capabilities['amd_amf']:
                        logger.info("  ✅ AMD 硬體編碼支援")
            if self.target_fps:
                logger.info(f"目標FPS: {self.target_fps}")
            if self.target_resolution:
                logger.info(f"目標解析度: {self.target_resolution[0]}x{self.target_resolution[1]}")
            logger.info(f"輸出路徑: {self.output_path}")

    def _get_optimized_video_settings(self, original_fps, original_width, original_height):
        """獲取優化的視頻設定"""
        quality_settings = {
            'high': {
                'fourcc': 'H264',
                'fps_factor': 1.0,
                'resolution_factor': 1.0,
                'description': '高品質 - 檔案較大，適合存檔'
            },
            'medium': {
                'fourcc': 'H264', 
                'fps_factor': 0.83,  # 25fps (from 30fps)
                'resolution_factor': 1.0,
                'description': '中等品質 - 平衡檔案大小和品質'
            },
            'low': {
                'fourcc': 'H264',
                'fps_factor': 0.67,  # 20fps
                'resolution_factor': 0.85,  # 約減少30%檔案大小
                'description': '低品質 - 檔案較小，適合長時間錄影'
            },
            'very_low': {
                'fourcc': 'X264',
                'fps_factor': 0.5,   # 15fps
                'resolution_factor': 0.75,  # 約減少45%檔案大小
                'description': '很低品質 - 檔案很小，適合監控存檔'
            }
        }
        
        settings = quality_settings.get(self.video_quality, quality_settings['medium'])
        
        # 計算優化後的FPS
        if self.target_fps:
            optimized_fps = self.target_fps
        else:
            optimized_fps = max(10, int(original_fps * settings['fps_factor']))
        
        # 計算優化後的解析度
        if self.target_resolution:
            optimized_width, optimized_height = self.target_resolution
        else:
            optimized_width = int(original_width * settings['resolution_factor'])
            optimized_height = int(original_height * settings['resolution_factor'])
        
        # 確保解析度是偶數（H.264要求）
        optimized_width = optimized_width if optimized_width % 2 == 0 else optimized_width - 1
        optimized_height = optimized_height if optimized_height % 2 == 0 else optimized_height - 1
        
        # 估算檔案大小減少比例
        size_reduction = 1 - (optimized_width * optimized_height * optimized_fps) / (original_width * original_height * original_fps)
        
        logger.info(f"視頻優化: {settings['description']}")
        logger.info(f"  原始設定: {original_width}x{original_height} @ {original_fps} FPS")
        logger.info(f"  優化設定: {optimized_width}x{optimized_height} @ {optimized_fps} FPS")
        logger.info(f"  編碼格式: {settings['fourcc']}")
        logger.info(f"  預估檔案大小減少: {size_reduction*100:.1f}%")
        
        return settings['fourcc'], optimized_fps, optimized_width, optimized_height

    def _load_multi_roi(self):
        """從文件加載多個ROI區域"""
        # 初始化公車區域
        self.bus_regions = []
        
        if os.path.exists(self.roi_file):
            try:
                with open(self.roi_file, 'r') as f:
                    data = json.load(f)
                    # 兼容舊格式：如果是點列表，轉換為新格式
                    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], list):
                        regions = [{'id': 0, 'name': 'Region_1', 'points': data, 'closed': True}]
                        self.bus_regions = []
                        logger.info(f"轉換舊格式ROI到新的多區域格式")
                    else:
                        regions = data.get('regions', [])
                        self.bus_regions = data.get('bus_regions', [])
                    logger.info(f"成功從 {self.roi_file} 加載 {len(regions)} 個人頭區域和 {len(self.bus_regions)} 個公車區域")
                    return regions
            except Exception as e:
                logger.error(f"加載ROI文件失敗: {e}")
        
        return []

    def _save_multi_roi(self):
        """保存多個ROI區域到文件"""
        try:
            data = {
                'regions': self.roi_regions,
                'bus_regions': self.bus_regions,
                'created_at': datetime.now().isoformat(),
                'total_regions': len(self.roi_regions),
                'total_bus_regions': len(self.bus_regions)
            }
            with open(self.roi_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"✅ 已保存 {len(self.roi_regions)} 個人頭區域和 {len(self.bus_regions)} 個公車區域到 {self.roi_file}")
            logger.info(f"   文件路徑: {os.path.abspath(self.roi_file)}")
        except Exception as e:
            logger.error(f"保存ROI文件失敗: {e}")

    def _add_new_region(self):
        """添加新的ROI區域"""
        if len(self.current_region_points) > 2:
            if self.current_region_type == 'person':
                region_id = len(self.roi_regions)
                region_name = f"Person_Region_{region_id + 1}"
                new_region = {
                    'id': region_id,
                    'name': region_name,
                    'type': 'person',
                    'points': self.current_region_points.copy(),
                    'closed': True,
                    'created_at': datetime.now().isoformat()
                }
                self.roi_regions.append(new_region)
                logger.info(f"添加新人頭區域: {region_name} (共 {len(self.roi_regions)} 個人頭區域)")
                self._save_multi_roi()  # 確保立即保存
                logger.info(f"人頭區域已保存到文件: {self.roi_file}")
            else:  # bus
                region_id = len(self.bus_regions)
                region_name = f"Bus_Region_{region_id + 1}"
                new_region = {
                    'id': region_id,
                    'name': region_name,
                    'type': 'bus',
                    'points': self.current_region_points.copy(),
                    'closed': True,
                    'created_at': datetime.now().isoformat()
                }
                self.bus_regions.append(new_region)
                logger.info(f"添加新公車區域: {region_name} (共 {len(self.bus_regions)} 個公車區域)")
                self._save_multi_roi()  # 確保立即保存
                logger.info(f"公車區域已保存到文件: {self.roi_file}")
            
            self.current_region_points = []
            self.current_region_closed = False
            return region_id
        return -1

    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數用於繪製多個ROI區域（增強編輯功能）"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 檢查是否在編輯模式
            if self.edit_mode and self.selected_region_index >= 0:
                self._handle_edit_mode_click(x, y)
                return
            
            # 左鍵點擊：添加點到當前區域
            if self.current_region_closed:
                logger.warning("當前區域已關閉，請按 'n' 開始新人頭區域或 'b' 開始新公車區域")
                return
            self.current_region_points.append([x, y])
            logger.info(f"添加ROI點到當前{self.current_region_type}區域: ({x}, {y}) - 點數: {len(self.current_region_points)}")

        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右鍵點擊：關閉當前區域或退出編輯模式
            if self.edit_mode:
                self._exit_edit_mode()
                return
                
            if not self.current_region_closed and len(self.current_region_points) > 2:
                region_id = self._add_new_region()
                if region_id >= 0:
                    self.current_region_closed = True
                    region_type = self.current_region_type
                    logger.info(f"✅ {region_type}區域 {region_id + 1} 已關閉並自動保存到 {self.roi_file}")
                    # 強制保存確認
                    self._save_multi_roi()
                else:
                    logger.warning("區域點數不足，無法關閉")

        elif event == cv2.EVENT_MBUTTONDOWN:
            # 中鍵點擊：選擇/取消選擇區域（用於編輯）
            self._select_region_at_point(x, y)
            
        elif event == cv2.EVENT_LBUTTONDBLCLK:
            # 雙擊左鍵：進入編輯模式
            if not self.edit_mode:
                self._enter_edit_mode_at_point(x, y)
                
        elif event == cv2.EVENT_MOUSEMOVE:
            # 鼠標移動：處理拖拽
            if self.edit_mode and self.drag_mode:
                self._handle_mouse_drag(x, y)
                
        elif event == cv2.EVENT_LBUTTONUP:
            # 左鍵釋放：結束拖拽
            if self.edit_mode and self.drag_mode:
                self.drag_mode = False
                self._save_multi_roi()  # 保存拖拽結果
                logger.info("點移動完成，已保存")

    def _select_region_at_point(self, x, y):
        """選擇指定點所在的區域（支持人頭和公車區域，擴大選擇範圍）"""
        # 擴大選擇範圍：檢查點是否在區域邊界附近
        selection_tolerance = 20  # 增加容差到20像素
        
        logger.info(f"🖱️ 中鍵點擊位置: ({x}, {y})")
        
        # 收集所有可能的候選區域
        candidates = []
        
        # 檢查人頭區域
        for i, region in enumerate(self.roi_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)
                
                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('person', i, region, 0))  # 0表示在內部，優先級最高
                    continue
                
                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('person', i, region, distance))
        
        # 檢查公車區域
        for i, region in enumerate(self.bus_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)
                
                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('bus', i, region, 0))  # 0表示在內部，優先級最高
                    continue
                
                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('bus', i, region, distance))
        
        if candidates:
            # 按距離排序，優先選擇距離最近的（內部點距離為0）
            candidates.sort(key=lambda x: x[3])
            
            # 如果有多個候選，優先選擇公車區域（因為通常比較難選中）
            bus_candidates = [c for c in candidates if c[0] == 'bus' and c[3] == 0]
            if bus_candidates:
                region_type, region_index, region, distance = bus_candidates[0]
            else:
                region_type, region_index, region, distance = candidates[0]
            
            self.selected_region_index = region_index
            self.selected_region_type = region_type
            
            region_name = region.get('name', f'{region_type.title()}_Region_{region_index + 1}')
            distance_info = "(內部)" if distance == 0 else f"(邊界附近 {distance:.1f}px)"
            logger.info(f"✅ 選中{region_type}區域 {region_index + 1}: {region_name} {distance_info}")
            
            # 顯示所有候選區域信息
            if len(candidates) > 1:
                logger.info(f"   發現 {len(candidates)} 個候選區域，已選擇最佳匹配")
                for i, (rtype, ridx, reg, dist) in enumerate(candidates):
                    marker = "👉" if i == 0 else "  "
                    logger.info(f"   {marker} {rtype}區域 {ridx + 1}: 距離 {dist:.1f}px")
            
            return
        
        self.selected_region_index = -1
        self.selected_region_type = 'person'
        logger.info("❌ 未選中任何區域 - 請點擊區域內部或邊界附近")
        logger.info(f"   提示: 容差範圍為 {selection_tolerance} 像素，或使用數字鍵快速選擇")

    def _select_region_by_number(self, region_num):
        """使用數字鍵快速選擇區域"""
        logger.info(f"🔍 嘗試選擇區域 {region_num + 1}")
        logger.info(f"   人頭區域數: {len(self.roi_regions)}, 公車區域數: {len(self.bus_regions)}")
        
        # 先檢查人頭區域
        if region_num < len(self.roi_regions):
            self.selected_region_index = region_num
            self.selected_region_type = 'person'
            region_name = self.roi_regions[region_num].get('name', f'Person_Region_{region_num + 1}')
            logger.info(f"✅ 快速選中人頭區域 {region_num + 1}: {region_name}")
            logger.info(f"   設置: selected_region_index={self.selected_region_index}, type={self.selected_region_type}")
            return
        
        # 再檢查公車區域
        bus_index = region_num - len(self.roi_regions)
        logger.info(f"   計算公車索引: {region_num} - {len(self.roi_regions)} = {bus_index}")
        
        if bus_index >= 0 and bus_index < len(self.bus_regions):
            self.selected_region_index = bus_index  # 這裡應該是bus_index，不是region_num
            self.selected_region_type = 'bus'
            region_name = self.bus_regions[bus_index].get('name', f'Bus_Region_{bus_index + 1}')
            logger.info(f"✅ 快速選中公車區域 {bus_index + 1}: {region_name}")
            logger.info(f"   設置: selected_region_index={self.selected_region_index}, type={self.selected_region_type}")
            return
        
        # 沒有找到對應的區域
        total_regions = len(self.roi_regions) + len(self.bus_regions)
        logger.warning(f"❌ 區域 {region_num + 1} 不存在 (總共有 {total_regions} 個區域)")
        logger.info("💡 提示: 按 'i' 查看所有區域信息")
        if len(self.roi_regions) > 0:
            logger.info(f"   人頭區域: 按 1-{len(self.roi_regions)}")
        if len(self.bus_regions) > 0:
            start_num = len(self.roi_regions) + 1
            end_num = len(self.roi_regions) + len(self.bus_regions)
            logger.info(f"   公車區域: 按 {start_num}-{end_num}")

    def _enter_edit_mode_at_point(self, x, y):
        """在指定點進入編輯模式"""
        self._select_region_at_point(x, y)
        if self.selected_region_index >= 0:
            self.edit_mode = True
            self.edit_point_index = -1
            logger.info(f"進入編輯模式 - {self.selected_region_type}區域 {self.selected_region_index + 1}")
            logger.info("編輯模式操作說明:")
            logger.info("  - 左鍵點擊點附近: 選擇並拖拽點")
            logger.info("  - 左鍵點擊邊線: 在該位置插入新點")
            logger.info("  - 右鍵點擊: 退出編輯模式")
            logger.info("  - Delete鍵: 刪除選中的點")
        else:
            logger.warning("未找到可編輯的區域")

    def _exit_edit_mode(self):
        """退出編輯模式"""
        if self.edit_mode:
            self.edit_mode = False
            self.edit_point_index = -1
            self.drag_mode = False
            self._save_multi_roi()  # 保存編輯結果
            logger.info("已退出編輯模式並保存變更")

    def _handle_edit_mode_click(self, x, y):
        """處理編輯模式下的點擊事件"""
        if self.selected_region_index < 0:
            return
            
        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                return
            region = self.bus_regions[self.selected_region_index]
        
        points = region['points']
        
        # 檢查是否點擊了現有的點
        for i, point in enumerate(points):
            distance = np.sqrt((x - point[0])**2 + (y - point[1])**2)
            if distance <= self.edit_threshold:
                self.edit_point_index = i
                self.drag_mode = True
                logger.info(f"選中點 {i + 1}，可拖拽移動")
                return
        
        # 檢查是否點擊了邊線，如果是則插入新點
        self._insert_point_on_edge(x, y, points)

    def _insert_point_on_edge(self, x, y, points):
        """在邊線上插入新點"""
        min_distance = float('inf')
        insert_index = -1
        
        for i in range(len(points)):
            p1 = points[i]
            p2 = points[(i + 1) % len(points)]
            
            # 計算點到線段的距離
            distance = self._point_to_line_distance(x, y, p1, p2)
            
            if distance < self.edit_threshold and distance < min_distance:
                min_distance = distance
                insert_index = i + 1
        
        if insert_index >= 0:
            points.insert(insert_index, [x, y])
            self.edit_point_index = insert_index
            self._save_multi_roi()
            logger.info(f"在位置 {insert_index} 插入新點: ({x}, {y})")

    def _point_to_line_distance(self, px, py, p1, p2):
        """計算點到線段的距離"""
        x1, y1 = p1
        x2, y2 = p2
        
        # 線段長度的平方
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2
        
        if line_length_sq == 0:
            return np.sqrt((px - x1)**2 + (py - y1)**2)
        
        # 計算投影參數
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_length_sq))
        
        # 投影點
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # 返回距離
        return np.sqrt((px - proj_x)**2 + (py - proj_y)**2)

    def _handle_mouse_drag(self, x, y):
        """處理鼠標拖拽事件"""
        if self.edit_mode and self.drag_mode and self.edit_point_index >= 0:
            # 獲取當前選中的區域
            if self.selected_region_type == 'person':
                if self.selected_region_index >= len(self.roi_regions):
                    return
                region = self.roi_regions[self.selected_region_index]
            else:  # bus
                if self.selected_region_index >= len(self.bus_regions):
                    return
                region = self.bus_regions[self.selected_region_index]
            
            # 更新點的位置
            if self.edit_point_index < len(region['points']):
                region['points'][self.edit_point_index] = [x, y]

    def _delete_selected_point(self):
        """刪除選中的點"""
        if not self.edit_mode or self.edit_point_index < 0 or self.selected_region_index < 0:
            logger.warning("沒有選中的點可刪除")
            return
            
        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                return
            region = self.bus_regions[self.selected_region_index]
        
        points = region['points']
        
        # 確保至少保留3個點
        if len(points) <= 3:
            logger.warning("區域至少需要3個點，無法刪除")
            return
        
        # 刪除點
        if self.edit_point_index < len(points):
            deleted_point = points.pop(self.edit_point_index)
            self.edit_point_index = -1
            self._save_multi_roi()
            logger.info(f"已刪除點: {deleted_point}")

    def _clear_all_roi(self):
        """清除所有ROI區域"""
        self.roi_regions = []
        self.bus_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        self.region_statistics = {}
        self.bus_statistics = {}
        if os.path.exists(self.roi_file):
            os.remove(self.roi_file)
        logger.info("所有ROI區域已清除（包含人頭和公車區域）")

    def _start_new_region(self, region_type='person'):
        """開始繪製新區域"""
        if not self.current_region_closed and len(self.current_region_points) > 0:
            logger.warning("當前區域未完成，請先右鍵關閉當前區域")
            return
        self.current_region_points = []
        self.current_region_closed = False
        self.current_region_type = region_type
        logger.info(f"開始繪製新{region_type}區域")

    def _delete_selected_region(self):
        """刪除選中的區域（支持人頭和公車區域）"""
        if self.selected_region_index < 0:
            logger.warning("沒有選中的區域可刪除")
            return
            
        if self.selected_region_type == 'person':
            if self.selected_region_index < len(self.roi_regions):
                deleted_region = self.roi_regions.pop(self.selected_region_index)
                # 重新分配ID
                for i, region in enumerate(self.roi_regions):
                    region['id'] = i
                    region['name'] = f"Person_Region_{i + 1}"
                logger.info(f"已刪除人頭區域: {deleted_region['name']}")
            else:
                logger.warning("選中的人頭區域索引無效")
        else:  # bus
            if self.selected_region_index < len(self.bus_regions):
                deleted_region = self.bus_regions.pop(self.selected_region_index)
                # 重新分配ID
                for i, region in enumerate(self.bus_regions):
                    region['id'] = i
                    region['name'] = f"Bus_Region_{i + 1}"
                logger.info(f"已刪除公車區域: {deleted_region['name']}")
            else:
                logger.warning("選中的公車區域索引無效")
        
        self.selected_region_index = -1
        self.selected_region_type = 'person'
        self.edit_mode = False
        self.edit_point_index = -1
        self.drag_mode = False
        self._save_multi_roi()

    def _update_statistics(self, region_results, total_count):
        """更新統計數據"""
        # 更新總統計
        self.total_statistics['total_count'] = total_count
        self.total_statistics['region_counts'] = [r['count'] for r in region_results]
        self.total_statistics['detection_history'].append({
            'timestamp': datetime.now().isoformat(),
            'total_count': total_count,
            'region_counts': {r['region_name']: r['count'] for r in region_results}
        })

        # 保持歷史記錄在合理範圍內
        if len(self.total_statistics['detection_history']) > 1000:
            self.total_statistics['detection_history'] = self.total_statistics['detection_history'][-1000:]

        # 更新各區域統計
        for region_result in region_results:
            region_name = region_result['region_name']
            if region_name not in self.region_statistics:
                self.region_statistics[region_name] = {
                    'total_detections': 0,
                    'max_count': 0,
                    'avg_count': 0,
                    'detection_count': 0
                }

            stats = self.region_statistics[region_name]
            stats['total_detections'] += region_result['count']
            stats['max_count'] = max(stats['max_count'], region_result['count'])
            stats['detection_count'] += 1
            stats['avg_count'] = stats['total_detections'] / stats['detection_count']

    def _update_bus_statistics(self, bus_results, bus_total_count):
        """更新公車統計數據"""
        # 更新公車統計
        for bus_result in bus_results:
            region_name = bus_result['region_name']
            if region_name not in self.bus_statistics:
                self.bus_statistics[region_name] = {
                    'total_detections': 0,
                    'max_count': 0,
                    'avg_count': 0,
                    'detection_count': 0
                }

            stats = self.bus_statistics[region_name]
            stats['total_detections'] += bus_result['count']
            stats['max_count'] = max(stats['max_count'], bus_result['count'])
            stats['detection_count'] += 1
            stats['avg_count'] = stats['total_detections'] / stats['detection_count']

    def _get_statistics_summary(self):
        """獲取統計摘要"""
        summary = {
            'total_regions': len(self.roi_regions),
            'current_total': self.total_statistics['total_count'],
            'region_stats': self.region_statistics.copy()
        }
        return summary

    def _draw_roi_regions(self, frame):
        """繪製所有ROI區域"""
        # 定義人頭區域的顏色
        person_colors = [
            (0, 255, 255),  # 黃色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 橙藍色
        ]
        
        # 定義公車區域的顏色（藍色系列）
        bus_colors = [
            (255, 0, 0),    # 藍色
            (255, 100, 0),  # 深藍色
            (255, 150, 0),  # 中藍色
            (255, 200, 0),  # 淺藍色
            (255, 50, 50),  # 藍紫色
            (255, 0, 100),  # 深藍紫色
        ]

        # 繪製已完成的人頭區域
        for i, region in enumerate(self.roi_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                color = person_colors[i % len(person_colors)]
                roi_np = np.array([region['points']], dtype=np.int32)

                # 如果是選中的區域，使用稍粗的線條，否則使用細線
                is_selected = (i == self.selected_region_index and self.selected_region_type == 'person')
                thickness = 3 if is_selected else 1
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)

                # 如果處於編輯模式且是選中的區域，繪製編輯點
                if self.edit_mode and is_selected:
                    self._draw_edit_points(frame, region['points'], color)

                # 添加區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    region_name = region.get('name', f'Person_Region_{i+1}')
                    label_text = f"{region_name} [EDIT]" if (self.edit_mode and is_selected) else region_name
                    cv2.putText(frame, label_text,
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        # 繪製已完成的公車區域
        for i, region in enumerate(self.bus_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                color = bus_colors[i % len(bus_colors)]
                roi_np = np.array([region['points']], dtype=np.int32)

                # 公車區域線條，選中時稍粗，否則使用細線
                is_selected = (i == self.selected_region_index and self.selected_region_type == 'bus')
                thickness = 3 if is_selected else 1
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)

                # 如果處於編輯模式且是選中的區域，繪製編輯點
                if self.edit_mode and is_selected:
                    self._draw_edit_points(frame, region['points'], color)

                # 添加區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    region_name = region.get('name', f'Bus_Region_{i+1}')
                    label_text = f"{region_name} [EDIT]" if (self.edit_mode and is_selected) else region_name
                    cv2.putText(frame, label_text,
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # 繪製當前正在繪製的區域
        if len(self.current_region_points) > 0:
            # 根據當前區域類型選擇顏色
            if self.current_region_type == 'bus':
                current_color = (255, 0, 0)  # 藍色表示正在繪製公車區域
                region_text = f"Drawing Bus Region: {len(self.current_region_points)} points"
            else:
                current_color = (0, 0, 255)  # 紅色表示正在繪製人頭區域
                region_text = f"Drawing Person Region: {len(self.current_region_points)} points"

            if len(self.current_region_points) > 1:
                roi_np = np.array([self.current_region_points], dtype=np.int32)
                thickness = 1  # 統一使用細線
                cv2.polylines(frame, roi_np, isClosed=False, color=current_color, thickness=thickness)

            # 繪製所有點
            point_radius = 6 if self.current_region_type == 'bus' else 5
            for point in self.current_region_points:
                cv2.circle(frame, tuple(point), point_radius, current_color, -1)

            # 顯示當前點數和區域類型
            if len(self.current_region_points) > 0:
                cv2.putText(frame, region_text, (10, frame.shape[0] - 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, current_color, 2)

    def _draw_edit_points(self, frame, points, color):
        """繪製編輯模式下的控制點"""
        for i, point in enumerate(points):
            x, y = point
            
            # 繪製控制點
            if i == self.edit_point_index:
                # 選中的點用較大的圓圈和不同顏色
                cv2.circle(frame, (x, y), 8, (0, 255, 255), 2)  # 黃色圓圈
                cv2.circle(frame, (x, y), 4, (0, 0, 255), -1)   # 紅色實心
            else:
                # 普通控制點
                cv2.circle(frame, (x, y), 6, color, 2)
                cv2.circle(frame, (x, y), 3, (255, 255, 255), -1)
            
            # 顯示點的編號
            cv2.putText(frame, str(i + 1), (x + 10, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 顯示編輯模式提示
        if self.edit_mode:
            edit_info = f"Edit Mode: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
            if self.edit_point_index >= 0:
                edit_info += f" | Selected Point: {self.edit_point_index + 1}"
            cv2.putText(frame, edit_info, (10, frame.shape[0] - 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    def _draw_status_info(self, frame):
        """繪製當前狀態信息"""
        # 左下角顯示編輯模式狀態（大字體，醒目）
        if self.edit_mode:
            edit_text = "EDIT MODE"
            edit_color = (0, 255, 255)  # 黃色表示編輯模式
            # 左下角位置
            edit_y = frame.shape[0] - 30
            cv2.putText(frame, edit_text, (10, edit_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, edit_color, 3)
            
            # 編輯模式詳細信息
            if self.selected_region_index >= 0:
                detail_text = f"Editing: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
                cv2.putText(frame, detail_text, (10, edit_y - 35), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, edit_color, 2)
                
                if self.edit_point_index >= 0:
                    point_text = f"Selected Point: {self.edit_point_index + 1}"
                    cv2.putText(frame, point_text, (10, edit_y - 65), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, edit_color, 2)
        
        # 右下角顯示其他狀態信息
        y_start = frame.shape[0] - 150
        x_right = frame.shape[1] - 400  # 右側位置
        
        # 顯示選中區域狀態
        if self.selected_region_index >= 0:
            status_text = f"Selected: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
            color = (0, 255, 0)  # 綠色表示已選中
        else:
            status_text = "No Region Selected (Middle-click to select)"
            color = (0, 0, 255)  # 紅色表示未選中
        
        cv2.putText(frame, status_text, (x_right, y_start), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 顯示編輯模式提示（非編輯模式時）
        if not self.edit_mode:
            edit_hint = "Press 'e' to enter EDIT MODE"
            cv2.putText(frame, edit_hint, (x_right, y_start + 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
        else:
            exit_hint = "Press 'e' or ESC to exit EDIT MODE"
            cv2.putText(frame, exit_hint, (x_right, y_start + 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 顯示快捷鍵提示
        shortcuts = [
            "Shortcuts: 'v'=Toggle Display, 'e'=Edit, '1-9'=Select Region, 'n'=New Person, 'b'=New Bus, 'd'=Delete, 'h'=Help"
        ]
        
        cv2.putText(frame, shortcuts[0], (10, y_start + 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 顯示區域編號提示
        if len(self.roi_regions) > 0 or len(self.bus_regions) > 0:
            region_info = f"Regions: Person(1-{len(self.roi_regions)})"
            if len(self.bus_regions) > 0:
                start_num = len(self.roi_regions) + 1
                end_num = len(self.roi_regions) + len(self.bus_regions)
                region_info += f", Bus({start_num}-{end_num})"
            
            cv2.putText(frame, region_info, (10, y_start + 75), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

    def _print_region_info(self):
        """打印區域信息"""
        logger.info("=== 多區域ROI信息 ===")
        logger.info(f"人頭區域數: {len(self.roi_regions)}")
        logger.info(f"公車區域數: {len(self.bus_regions)}")

        for i, region in enumerate(self.roi_regions):
            status = "選中" if i == self.selected_region_index else "未選中"
            logger.info(f"人頭區域 {i+1}: {region.get('name', f'Person_Region_{i+1}')} - 點數: {len(region['points'])} - 狀態: {status}")

        for i, region in enumerate(self.bus_regions):
            logger.info(f"公車區域 {i+1}: {region.get('name', f'Bus_Region_{i+1}')} - 點數: {len(region['points'])}")

        if len(self.current_region_points) > 0:
            logger.info(f"正在繪製 {self.current_region_type} 區域: {len(self.current_region_points)} 個點")

        # 顯示統計信息
        stats = self._get_statistics_summary()
        logger.info(f"當前總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"  {region_name}: 平均 {region_stats['avg_count']:.1f}, "
                       f"最大 {region_stats['max_count']}, "
                       f"檢測次數 {region_stats['detection_count']}")

    def _print_help(self):
        """打印幫助信息"""
        help_text = f"""
=== 多區域頭部和公車檢測控制說明（增強編輯版） ===
🖱️ 鼠標操作:
  左鍵點擊: 添加點到當前區域 / 編輯模式下選擇並拖拽點
  右鍵點擊: 完成當前區域繪製 / 退出編輯模式
  中鍵點擊: 選擇/取消選擇區域
  雙擊左鍵: 進入編輯模式（在區域內雙擊）
  拖拽: 編輯模式下移動選中的點

⌨️ 鍵盤操作:
  'q': 退出程序
  'c': 清除所有ROI區域
  'n': 開始繪製新人頭區域
  'b': 開始繪製新公車區域 ⭐NEW⭐
  'd': 刪除選中的區域 / 編輯模式下刪除選中的點
  'e': 進入/退出編輯模式（需先選中區域）
  '1-9': 快速選擇區域（按數字鍵） ⭐NEW⭐
  'ESC': 退出編輯模式
  's': 保存當前幀
  'i': 顯示區域信息
  'h': 顯示此幫助信息

📝 區域繪製流程:
1. 按 'n' 開始新人頭區域 或 按 'b' 開始新公車區域
2. 左鍵點擊添加點 (至少3個點)
3. 右鍵點擊完成區域並自動保存
4. 重複步驟1-3添加更多區域

✏️ 區域編輯流程:
1. 中鍵點擊選擇要編輯的區域
2. 雙擊左鍵或按 'e' 進入編輯模式
3. 左鍵點擊點附近選擇並拖拽移動
4. 左鍵點擊邊線插入新點
5. 按 'd' 刪除選中的點
6. 右鍵或ESC退出編輯模式

🔍 檢測說明:
- 人頭檢測: 紅色線框，標籤P1, P2... (YOLOv5s模型)
- 公車檢測: 藍色線框，標籤B1, B2... (YOLOv5s模型)
- 選中區域: 較粗線條顯示
- 編輯模式: 顯示控制點和 [EDIT] 標籤
- 左上角顯示: Person Count 和 Bus Count

⚡ 檢測間隔控制:
- 當前間隔: 每 {self.detection_interval} 幀檢測一次
- DETECT: 執行實際檢測
- CACHED: 使用緩存結果
- 可通過 --detection-interval 參數調整

💡 編輯提示:
- 編輯模式下選中的點會以黃色圓圈標示
- 控制點顯示編號便於識別
- 區域至少需要3個點，無法刪除到少於3個點
- 所有編輯操作會自動保存
        """
        logger.info(help_text)

    def connect_stream(self):
        """连接RTSP流 - 支援硬體解碼加速"""
        try:
            # ⭐NEW⭐ 嘗試使用硬體解碼
            if self.use_hardware_decoding and HARDWARE_DECODER_AVAILABLE:
                logger.info("🚀 嘗試使用硬體解碼加速...")
                try:
                    self.hardware_decoder = HardwareVideoDecoder(self.rtsp_url, auto_detect=True)
                    self.cap = self.hardware_decoder.create_capture()
                    
                    if self.cap and self.cap.isOpened():
                        decoder_info = self.hardware_decoder.get_decoder_info()
                        logger.info(f"✅ 硬體解碼啟用: {decoder_info['decoder_type']}")
                        
                        # 執行簡單的性能測試
                        benchmark_results = self.hardware_decoder.benchmark_decoder(test_frames=5)
                        if benchmark_results and benchmark_results['success_rate'] > 80:
                            logger.info(f"🏃 硬體解碼性能測試通過: {benchmark_results['avg_fps']:.1f} FPS")
                        else:
                            logger.warning("⚠️ 硬體解碼性能不佳，回退到標準解碼")
                            self.cap.release()
                            self.cap = None
                            self.hardware_decoder = None
                    else:
                        logger.warning("⚠️ 硬體解碼初始化失敗，回退到標準解碼")
                        self.hardware_decoder = None
                        
                except Exception as e:
                    logger.warning(f"⚠️ 硬體解碼設置失敗: {e}，回退到標準解碼")
                    self.hardware_decoder = None
                    self.cap = None
            
            # 如果硬體解碼失敗或未啟用，使用標準解碼
            if self.cap is None:
                logger.info("📱 使用標準視頻解碼...")
                self.cap = cv2.VideoCapture(self.rtsp_url)
                # 優化RTSP流設置以減少延遲
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小緩衝區
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 3000)
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)
                # 設置為實時流模式
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
                self.cap.set(cv2.CAP_PROP_FPS, 25)  # 限制FPS以減少延遲
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 保存原始設定
            self.original_fps = fps
            self.original_width = width
            self.original_height = height
            
            # 顯示解碼信息
            decoder_type = "硬體解碼" if self.hardware_decoder else "標準解碼"
            if self.hardware_decoder:
                decoder_info = self.hardware_decoder.get_decoder_info()
                decoder_type = f"硬體解碼 ({decoder_info['decoder_type']})"
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps ({decoder_type})")
            
            if self.save_video:
                # 獲取優化的視頻設定
                fourcc_str, opt_fps, opt_width, opt_height = self._get_optimized_video_settings(fps, width, height)
                
                # 保存優化後的設定供後續使用
                self.output_fps = opt_fps
                self.output_width = opt_width
                self.output_height = opt_height
                
                # 嘗試使用硬體編碼
                if (self.use_hardware_encoding and self.hardware_capabilities and 
                    self.hardware_capabilities['ffmpeg_available'] and 
                    self.hardware_capabilities['recommended_codec'] != 'H264'):
                    
                    try:
                        logger.info("🚀 嘗試使用硬體編碼...")
                        self.out = HardwareVideoWriter(
                            self.output_path, opt_fps, opt_width, opt_height,
                            codec=self.hardware_capabilities['recommended_codec'],
                            quality=self.video_quality
                        )
                        
                        if self.out.isOpened():
                            logger.info(f"✅ 硬體編碼視頻將保存到: {self.output_path}")
                            logger.info(f"   編碼器: {self.hardware_capabilities['recommended_codec']}")
                            logger.info(f"   輸出設定: {opt_width}x{opt_height} @ {opt_fps}fps")
                        else:
                            raise Exception("硬體編碼器初始化失敗")
                            
                    except Exception as e:
                        logger.warning(f"⚠️ 硬體編碼失敗，回退到軟體編碼: {e}")
                        self.out = None
                
                # 回退到 OpenCV 軟體編碼
                if not hasattr(self, 'out') or self.out is None:
                    logger.info("📹 使用 OpenCV 軟體編碼")
                    fourcc = cv2.VideoWriter_fourcc(*fourcc_str)
                    self.out = cv2.VideoWriter(self.output_path, fourcc, opt_fps, (opt_width, opt_height))
                    
                    if not self.out.isOpened():
                        logger.error(f"❌ 無法創建視頻寫入器: {self.output_path}")
                        self.save_video = False
                    else:
                        logger.info(f"✅ 軟體編碼視頻將保存到: {self.output_path} ({fourcc_str}格式)")
                        logger.info(f"   輸出設定: {opt_width}x{opt_height} @ {opt_fps}fps")
            
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """GPU优化的流处理 - 支援動態顯示切換"""
        if not self.connect_stream():
            return

        # 初始顯示設定
        self.display_enabled = display
        if self.display_enabled:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)
            self.window_created = True

        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                frame_start = time.time()
                
                # 清理緩衝區以減少延遲（每10幀清理一次）
                if frame_count % 10 == 0:
                    # 快速讀取並丟棄積累的幀
                    for _ in range(2):
                        ret_temp, _ = self.cap.read()
                        if not ret_temp:
                            break
                
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    # 重新連接時清理緩衝區
                    if self.cap.isOpened():
                        self.cap.release()
                    if not self.connect_stream():
                        time.sleep(1)
                        continue
                    else:
                        continue
                
                frame_count += 1

                # 檢測間隔控制 - 修復版本
                should_detect = (frame_count % self.detection_interval == 1)

                if should_detect or self.detection_interval == 1:
                    # 執行檢測（每幀檢測或到達檢測間隔）
                    detection_start = time.time()
                    
                    # 人頭檢測使用主模型
                    with torch.no_grad():
                        if self.detector.device == 'cuda':
                            torch.cuda.synchronize()
                        head_results = self.detector.model([frame])
                        if self.detector.device == 'cuda':
                            torch.cuda.synchronize()
                    
                    # 車輛檢測使用專用模型（只在有車輛ROI時）
                    vehicle_results = None
                    if self.bus_regions and len(self.bus_regions) > 0:
                        with torch.no_grad():
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                            vehicle_results = self.detector.vehicle_model([frame])
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                    
                    multi_region_result = self.detector._process_multi_region_frame(
                        frame, head_results, vehicle_results, self.roi_regions, self.bus_regions, frame_count)
                    detection_time = time.time() - detection_start

                    # 緩存檢測結果
                    self.last_detection_result = multi_region_result
                    self.detection_frame_count += 1
                else:
                    # 警告：使用緩存結果可能不準確
                    if self.last_detection_result is not None:
                        multi_region_result = self.last_detection_result.copy()
                        # 重新繪製當前幀
                        bus_results = multi_region_result.get('bus_results', [])
                        bus_count = multi_region_result.get('bus_count', 0)
                        multi_region_result['annotated_frame'] = self.detector._draw_multi_region_results(
                            frame, multi_region_result['region_results'], multi_region_result['total_count'], 
                            bus_results, bus_count)
                        
                        # 添加緩存警告標示
                        cv2.putText(multi_region_result['annotated_frame'], 
                                   "CACHED RESULT - MAY BE OUTDATED", 
                                   (10, frame.shape[0] - 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)
                    else:
                        # 如果沒有緩存結果，執行一次檢測
                        detection_start = time.time()
                        
                        # 人頭檢測使用主模型
                        with torch.no_grad():
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                            head_results = self.detector.model([frame])
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                        
                        # 車輛檢測使用專用模型（只在有車輛ROI時）
                        vehicle_results = None
                        if self.bus_regions and len(self.bus_regions) > 0:
                            with torch.no_grad():
                                if self.detector.device == 'cuda':
                                    torch.cuda.synchronize()
                                vehicle_results = self.detector.vehicle_model([frame])
                                if self.detector.device == 'cuda':
                                    torch.cuda.synchronize()
                        
                        multi_region_result = self.detector._process_multi_region_frame(
                            frame, head_results, vehicle_results, self.roi_regions, self.bus_regions, frame_count)
                        detection_time = time.time() - detection_start
                        self.last_detection_result = multi_region_result
                        self.detection_frame_count += 1
                    detection_time = 0  # 非檢測幀的檢測時間為0

                total_count = multi_region_result['total_count']
                region_results = multi_region_result['region_results']
                bus_count = multi_region_result.get('bus_count', 0)
                bus_results = multi_region_result.get('bus_results', [])
                annotated_frame = multi_region_result['annotated_frame']

                # 更新統計數據（只在檢測幀更新）
                if should_detect:
                    self._update_statistics(region_results, total_count)
                    # 更新公車統計
                    if bus_results:
                        self._update_bus_statistics(bus_results, bus_count)
                
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)
                
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                # 顯示FPS和檢測信息
                detection_status = "DETECT" if should_detect else "CACHED"
                fps_text = f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms | {detection_status}'
                cv2.putText(annotated_frame, fps_text, (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 顯示檢測間隔信息
                interval_text = f'Detection Interval: {self.detection_interval} frames'
                cv2.putText(annotated_frame, interval_text, (10, 180),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
                
                # 繪製多個ROI區域
                self._draw_roi_regions(annotated_frame)
                
                # 顯示當前狀態信息
                self._draw_status_info(annotated_frame)

                # 顯示處理 - 根據 display_enabled 決定顯示內容
                if self.window_created:
                    if self.display_enabled:
                        # 顯示完整的影片畫面
                        if annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                            cv2.imshow(self.window_name, annotated_frame)
                        else:
                            logger.warning("annotated_frame 無效，未顯示")
                    else:
                        # 只顯示偵測數字，不顯示影片
                        info_frame = np.zeros((400, 800, 3), dtype=np.uint8)
                        
                        # 顯示偵測數字和統計信息
                        y_pos = 40
                        cv2.putText(info_frame, f'Person Count: {total_count}', (20, y_pos),
                                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
                        y_pos += 50
                        
                        if bus_count > 0:
                            cv2.putText(info_frame, f'Bus Count: {bus_count}', (20, y_pos),
                                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 3)
                            y_pos += 50
                        
                        # 顯示各人頭區域計數
                        for region_result in region_results:
                            if region_result['region_id'] >= 0:
                                region_text = f"{region_result['region_name']}: {region_result['count']}"
                                cv2.putText(info_frame, region_text, (20, y_pos),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 165, 0), 2)
                                y_pos += 35
                        
                        # 顯示各公車區域計數
                        if bus_results:
                            for bus_result in bus_results:
                                if bus_result['region_id'] >= 0:
                                    bus_region_text = f"{bus_result['region_name']}: {bus_result['count']} buses"
                                    cv2.putText(info_frame, bus_region_text, (20, y_pos),
                                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
                                    y_pos += 35
                        
                        # 顯示FPS信息
                        detection_status = "DETECT" if should_detect else "CACHED"
                        fps_text = f'FPS: {fps:.1f} | {detection_status}'
                        cv2.putText(info_frame, fps_text, (20, y_pos),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                        y_pos += 40
                        
                        # 顯示狀態提示
                        status_text = "VIDEO HIDDEN - Press 'v' to show video"
                        cv2.putText(info_frame, status_text, (20, y_pos),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                        
                        cv2.imshow(self.window_name, info_frame)
                
                # 按鍵處理（無論顯示狀態如何都要檢查）
                key = cv2.waitKey(1) & 0xFF if self.window_created else 255
                
                # 調試：顯示按鍵信息
                if key != 255:  # 255 表示沒有按鍵
                    logger.info(f"🔍 檢測到按鍵: '{chr(key) if 32 <= key <= 126 else f'Code:{key}'}' (ASCII: {key})")
                    
                    if key == ord('q'):
                        break
                    elif key == ord('v'):  # 新增：'v' 鍵切換顯示
                        self.toggle_display()
                    elif key == ord('c'):
                        self._clear_all_roi()
                    elif key == ord('n'):
                        self._start_new_region('person')
                    elif key == ord('b'):
                        self._start_new_region('bus')
                    elif key == ord('d'):
                        if self.edit_mode:
                            self._delete_selected_point()
                        else:
                            self._delete_selected_region()
                    elif key == ord('e'):
                        if self.selected_region_index >= 0:
                            if self.edit_mode:
                                self._exit_edit_mode()
                            else:
                                self.edit_mode = True
                                self.edit_point_index = -1
                                self.drag_mode = False
                                logger.info(f"✅ 進入編輯模式 - {self.selected_region_type}區域 {self.selected_region_index + 1}")
                                logger.info("編輯模式操作說明:")
                                logger.info("  - 左鍵點擊點附近: 選擇並拖拽點")
                                logger.info("  - 左鍵點擊邊線: 在該位置插入新點")
                                logger.info("  - 按 'd': 刪除選中的點")
                                logger.info("  - 右鍵或ESC: 退出編輯模式")
                        else:
                            logger.warning("⚠️ 請先選擇一個區域（中鍵點擊區域內部）")
                    elif key == 27:  # ESC鍵
                        if self.edit_mode:
                            self._exit_edit_mode()
                        else:
                            logger.info("按 'q' 退出程序")
                    elif key == ord('s'):
                        save_path = f"gpu_head_frame_{frame_count}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                    elif key == ord('i'):
                        self._print_region_info()
                    elif key == ord('h'):
                        self._print_help()
                    elif key >= ord('1') and key <= ord('9'):
                        # 數字鍵快速選擇區域
                        region_num = key - ord('1')  # 轉換為0-based索引
                        self._select_region_by_number(region_num)
                
                if self.save_video and self.out and annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                    # 如果輸出解析度與原始解析度不同，需要調整大小
                    if hasattr(self, 'output_width') and hasattr(self, 'output_height'):
                        if (annotated_frame.shape[1] != self.output_width or 
                            annotated_frame.shape[0] != self.output_height):
                            # 調整幀大小以匹配輸出設定
                            resized_frame = cv2.resize(annotated_frame, (self.output_width, self.output_height))
                            self.out.write(resized_frame)
                        else:
                            self.out.write(annotated_frame)
                    else:
                        self.out.write(annotated_frame)
                
                if frame_count % 100 == 0:
                    avg_fps = frame_count / elapsed_time
                    avg_gpu_memory = np.mean(self.gpu_memory_usage[-100:]) if self.gpu_memory_usage else 0
                    region_summary = ", ".join([f"{r['region_name']}: {r['count']}" for r in region_results])
                    detection_efficiency = (self.detection_frame_count / frame_count) * 100 if frame_count > 0 else 0
                    logger.info(f"帧 {frame_count}: 總人數 {total_count} ({region_summary}), "
                              f"平均FPS {avg_fps:.1f}, "
                              f"檢測效率 {detection_efficiency:.1f}% ({self.detection_frame_count}/{frame_count}), "
                              f"GPU内存 {avg_gpu_memory:.1f}MB")
                
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self._print_performance_summary()
            self.cleanup()
    
    def _print_performance_summary(self):
        """打印性能和統計總結"""
        if self.frame_times:
            avg_frame_time = np.mean(self.frame_times)
            avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0

            logger.info("=== GPU性能總結 ===")
            logger.info(f"平均幀處理時間: {avg_frame_time*1000:.1f}ms")
            logger.info(f"平均FPS: {avg_fps:.1f}")
            logger.info(f"檢測間隔設置: 每 {self.detection_interval} 幀檢測一次")
            logger.info(f"實際檢測幀數: {self.detection_frame_count}")

            if self.gpu_memory_usage:
                avg_gpu_memory = np.mean(self.gpu_memory_usage)
                max_gpu_memory = max(self.gpu_memory_usage)
                logger.info(f"平均GPU內存使用: {avg_gpu_memory:.1f}MB")
                logger.info(f"峰值GPU內存使用: {max_gpu_memory:.1f}MB")

        # 多區域統計總結
        logger.info("=== 多區域檢測統計總結 ===")
        logger.info(f"總ROI區域數: {len(self.roi_regions)}")

        stats = self._get_statistics_summary()
        logger.info(f"最終總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"區域 {region_name}:")
            logger.info(f"  - 平均人數: {region_stats['avg_count']:.1f}")
            logger.info(f"  - 最大人數: {region_stats['max_count']}")
            logger.info(f"  - 總檢測次數: {region_stats['detection_count']}")
            logger.info(f"  - 累計檢測人數: {region_stats['total_detections']}")

        # 保存統計數據到文件
        self._save_statistics_report()

    def _save_statistics_report(self):
        """保存統計報告到文件"""
        try:
            report_file = f"detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            report_data = {
                'session_info': {
                    'start_time': datetime.now().isoformat(),
                    'rtsp_url': self.rtsp_url,
                    'total_regions': len(self.roi_regions),
                    'total_bus_regions': len(self.bus_regions),
                    'device': self.detector.device,
                    'model_type': self.detector.model_type
                },
                'roi_regions': self.roi_regions,
                'bus_regions': self.bus_regions,
                'statistics': self.total_statistics,
                'region_statistics': self.region_statistics,
                'bus_statistics': self.bus_statistics,
                'performance': {
                    'avg_fps': 1.0 / np.mean(self.frame_times) if self.frame_times else 0,
                    'avg_frame_time_ms': np.mean(self.frame_times) * 1000 if self.frame_times else 0,
                    'avg_gpu_memory_mb': np.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0,
                    'max_gpu_memory_mb': max(self.gpu_memory_usage) if self.gpu_memory_usage else 0
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            logger.info(f"統計報告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"保存統計報告失敗: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        cv2.waitKey(1)
        
        # ⭐NEW⭐ 清理硬體解碼器
        if self.hardware_decoder:
            logger.info("🧹 清理硬體解碼器...")
            self.hardware_decoder = None
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 清理模型引用
        if hasattr(self.detector, 'model'):
            del self.detector.model
        if hasattr(self.detector, 'vehicle_model'):
            del self.detector.vehicle_model
            
        logger.info("资源清理完成（包含硬體解碼器和双模型）")

    def toggle_display(self):
        """動態切換顯示狀態"""
        current_time = time.time()
        
        # 防止快速切換（0.5秒內只能切換一次）
        if current_time - self.last_display_toggle_time < 0.5:
            return
            
        self.display_enabled = not self.display_enabled
        self.last_display_toggle_time = current_time
        
        # 確保視窗始終存在以接收按鍵輸入
        if not self.window_created:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)
            self.window_created = True
        
        if self.display_enabled:
            logger.info("🖥️ 顯示完整影片畫面 - 按 'v' 可切換到純數字模式")
        else:
            logger.info("📊 顯示純數字模式 - 按 'v' 可切換回完整影片畫面")

def main():
    parser = argparse.ArgumentParser(description='GPU优化的YOLOv5头部检测系统 (WebSocket版本)')
    parser.add_argument('--websocket-url', type=str,
                       default='ws://*************:8004',
                       help='WebSocket流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型类型')
    parser.add_argument('--conf-threshold', type=float, default=0.15,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['cpu', 'cuda', 'auto'], help='运行设备 (auto=自動偵測, cuda=強制GPU, cpu=強制CPU)')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='批处理大小（GPU优化）')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='gpu_head_detection.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    parser.add_argument('--detection-interval', type=int, default=50,
                       help='檢測間隔（幀數），例如5表示每5幀檢測一次，預設為10（每10幀檢測一次）')
    parser.add_argument('--video-quality', type=str, default='medium',
                       choices=['high', 'medium', 'low', 'very_low'],
                       help='視頻品質設定 (high=高品質大檔案, medium=平衡, low=小檔案, very_low=最小檔案)')
    parser.add_argument('--target-fps', type=int, default=None,
                       help='目標輸出FPS (覆蓋品質設定的FPS)')
    parser.add_argument('--target-resolution', type=str, default=None,
                       help='目標解析度，格式: WIDTHxHEIGHT (例如: 1280x720)')
    parser.add_argument('--use-hardware-encoding', action='store_true', default=True,
                       help='使用硬體編碼 (預設啟用)')
    parser.add_argument('--disable-hardware-encoding', action='store_true',
                       help='停用硬體編碼，強制使用軟體編碼')
    parser.add_argument('--use-hardware-decoding', action='store_true', default=True,
                       help='使用硬體解碼 (預設啟用) ⭐NEW⭐')
    parser.add_argument('--disable-hardware-decoding', action='store_true',
                       help='停用硬體解碼，強制使用軟體解碼 ⭐NEW⭐')

    args = parser.parse_args()
    
    # 處理硬體編碼與解碼設定
    use_hardware_encoding = args.use_hardware_encoding and not args.disable_hardware_encoding
    use_hardware_decoding = args.use_hardware_decoding and not args.disable_hardware_decoding
    
    # 顯示硬體加速狀態
    if use_hardware_encoding:
        logger.info("✅ 硬體編碼已啟用")
    else:
        logger.info("⚠️ 硬體編碼已停用")
        
    if use_hardware_decoding:
        logger.info("✅ 硬體解碼已啟用")
    else:
        logger.info("⚠️ 硬體解碼已停用")
    
    # 解析目標解析度
    target_resolution = None
    if args.target_resolution:
        try:
            width, height = map(int, args.target_resolution.split('x'))
            target_resolution = (width, height)
            logger.info(f"設定目標解析度: {width}x{height}")
        except ValueError:
            logger.error(f"無效的解析度格式: {args.target_resolution}，應為 WIDTHxHEIGHT")
            return 1

    # 驗證檢測間隔參數
    if args.detection_interval < 1:
        logger.warning("檢測間隔不能小於1，已重設為1")
        args.detection_interval = 1
    elif args.detection_interval > 30:
        logger.warning("檢測間隔過大，已限制為30幀")
        args.detection_interval = 30
    
    # 設備檢查邏輯更新
    if args.device == 'cuda':
        if not torch.cuda.is_available():
            logger.error("❌ CUDA不可用！请检查GPU驱动和CUDA安装")
            logger.info("💡 建議使用 --device auto 讓程式自動選擇最佳設備")
            return 1
        else:
            logger.info("🚀 強制使用CUDA GPU模式")
    elif args.device == 'cpu':
        logger.info("🖥️ 強制使用CPU模式")
    else:  # args.device == 'auto'
        logger.info("🔍 自動偵測模式 - 將選擇最佳可用設備")
    
    try:
        detector = GPUOptimizedHeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device,
            batch_size=args.batch_size
        )
        
        processor = GPUOptimizedWebSocketProcessor(
            websocket_url=args.websocket_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path,
            detection_interval=args.detection_interval,
            video_quality=args.video_quality,
            target_fps=args.target_fps,
            target_resolution=target_resolution,
            use_hardware_encoding=use_hardware_encoding
        )
        
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
