#!/usr/bin/env python3
"""
GPU优化的YOLOv5头部检测系统
专门针对GPU加速优化的RTSP视频流头部检测
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GPUOptimizedHeadDetector:
    def __init__(self, model_type='person', conf_threshold=0.15, device='cuda', batch_size=1):
        """
        GPU优化的头部检测器
        
        Args:
            model_type: 模型类型
            conf_threshold: 置信度阈值
            device: 运行设备
            batch_size: 批处理大小（GPU优化）
        """
        self.conf_threshold = conf_threshold
        self.device = device
        self.model_type = model_type
        self.batch_size = batch_size
        
        # COCO類別定義
        self.coco_classes = {
            0: 'person',
            5: 'bus',
            25: 'umbrella'
        }
        
        # 设置GPU优化参数
        self._setup_gpu_optimization()
        
        # 加载模型
        self._load_model()
        
        # 加载车辆检测专用模型
        self._load_vehicle_model()
        
    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if self.device == 'cuda' and torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            torch.cuda.empty_cache()
            
            gpu_count = torch.cuda.device_count()
            current_gpu = torch.cuda.current_device()
            gpu_name = torch.cuda.get_device_name(current_gpu)
            gpu_memory = torch.cuda.get_device_properties(current_gpu).total_memory / 1024**3
            
            logger.info(f"GPU优化设置:")
            logger.info(f"  - GPU数量: {gpu_count}")
            logger.info(f"  - 当前GPU: {current_gpu} ({gpu_name})")
            logger.info(f"  - GPU内存: {gpu_memory:.1f} GB")
            logger.info(f"  - CUDA版本: {torch.version.cuda}")
            logger.info(f"  - cuDNN启用: {torch.backends.cudnn.enabled}")
            logger.info(f"  - cuDNN基准: {torch.backends.cudnn.benchmark}")
            
        else:
            logger.warning("GPU不可用，使用CPU模式")
            self.device = 'cpu'
    
    def _load_model(self):
        """加载并优化模型"""
        try:
            logger.info("加载YOLOv5模型...")
            
            if self.model_type == 'custom':
                try:
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                    logger.info("加载专门的头部检测模型")
                except Exception:
                    logger.warning("未找到头部检测模型，使用人体检测模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', pretrained=True)
                    self.model_type = 'person'
            else:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m', pretrained=True)
            
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            self.model.iou = 0.5
            self.model.max_det = 1000
            
            if self.device == 'cuda':
                self.model.half()
                logger.info("启用FP16半精度推理")
                self._warmup_gpu()
            
            logger.info(f"模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _warmup_gpu(self):
        """GPU预热以获得稳定性能"""
        logger.info("GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(3):
                _ = self.model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("GPU预热完成")
    
    def _load_vehicle_model(self):
        """加载车辆检测专用模型 (YOLOv5n)"""
        try:
            logger.info("加载车辆检测专用模型 (YOLOv5n)...")
            
            # 加载轻量级YOLOv5n模型用于车辆检测
            self.vehicle_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.vehicle_model.to(self.device)
            self.vehicle_model.conf = 0.3  # 车辆检测使用更低的置信度阈值
            self.vehicle_model.iou = 0.45
            self.vehicle_model.max_det = 1000
            
            if self.device == 'cuda':
                self.vehicle_model.half()
                logger.info("车辆检测模型启用FP16半精度推理")
                # GPU预热车辆模型
                self._warmup_vehicle_gpu()
            
            logger.info(f"车辆检测模型加载完成，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"车辆检测模型加载失败: {e}")
            logger.warning("将使用主模型进行车辆检测")
            self.vehicle_model = self.model
    
    def _warmup_vehicle_gpu(self):
        """车辆检测模型GPU预热"""
        logger.info("车辆检测模型GPU预热中...")
        dummy_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        if self.device == 'cuda':
            dummy_input = dummy_input.half()
        
        with torch.no_grad():
            for _ in range(2):
                _ = self.vehicle_model(dummy_input)
        
        torch.cuda.synchronize()
        logger.info("车辆检测模型GPU预热完成")
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """从人体检测框中提取头部区域"""
        x1, y1, x2, y2 = person_bbox
        person_height = y2 - y1
        person_width = x2 - x1
        head_height = int(person_height * head_ratio)
        head_width_margin = int(person_width * 0.1)
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads_batch(self, frames, roi_regions=None, bus_regions=None):
        """批量檢測頭部和公車（GPU優化，支持多區域）"""
        if not isinstance(frames, list):
            frames = [frames]

        results_list = []

        try:
            # 人頭檢測使用主模型
            with torch.no_grad():
                if self.device == 'cuda':
                    torch.cuda.synchronize()
                head_results = self.model(frames)
                if self.device == 'cuda':
                    torch.cuda.synchronize()
            
            # 車輛檢測使用專用模型
            vehicle_results = None
            if bus_regions and len(bus_regions) > 0:
                with torch.no_grad():
                    if self.device == 'cuda':
                        torch.cuda.synchronize()
                    vehicle_results = self.vehicle_model(frames)
                    if self.device == 'cuda':
                        torch.cuda.synchronize()

            for i, frame in enumerate(frames):
                head_frame_results = head_results if len(frames) == 1 else [head_results[i]]
                vehicle_frame_results = vehicle_results if vehicle_results is None or len(frames) == 1 else [vehicle_results[i]]
                
                multi_region_results = self._process_multi_region_frame(
                    frame, head_frame_results, vehicle_frame_results, roi_regions, bus_regions, frame_count=i)
                results_list.append(multi_region_results)

            return results_list

        except Exception as e:
            logger.error(f"批量檢測過程中出錯: {e}")
            return [{'total_count': 0, 'region_results': [], 'bus_count': 0, 'bus_results': [], 'annotated_frame': frame} for frame in frames]
    
    def _process_multi_region_frame(self, frame, head_results, vehicle_results, roi_regions=None, bus_regions=None, frame_count=0):
        """處理多區域檢測結果（包含人頭和公車）"""
        try:
            # 處理人頭檢測結果
            head_detections = head_results.pandas().xyxy[0]
            all_head_bboxes = []
            annotated_frame = frame.copy()

            # 獲取人頭檢測結果
            if self.model_type == 'custom':
                target_head_detections = head_detections[head_detections['name'].isin(['head', 'person'])]
            else:
                target_head_detections = head_detections[head_detections['class'].isin([0, 25])]

            for _, detection in target_head_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else:
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                all_head_bboxes.append(bbox + [confidence])

            # 處理車輛檢測結果
            all_bus_bboxes = []
            if vehicle_results is not None:
                vehicle_detections = vehicle_results.pandas().xyxy[0]
                
                # 車輛檢測調試信息
                all_vehicle_classes = vehicle_detections['class'].unique() if len(vehicle_detections) > 0 else []
                if frame_count % 30 == 1:  # 每30幀顯示一次調試信息
                    logger.info(f"幀 {frame_count}: 車輛模型檢測到的所有類別: {sorted(all_vehicle_classes)}")
                    logger.info(f"車輛檢測總數量: {len(vehicle_detections)}")
                
                # 專門檢測公車（COCO類別5）
                bus_class = 5  # 只檢測公車
                
                all_detections = vehicle_detections[vehicle_detections['class'] == bus_class]
                if len(all_detections) > 0 and frame_count % 30 == 1:
                    logger.info(f"YOLOv5s檢測到公車候選: {len(all_detections)} 個")
                    for _, det in all_detections.iterrows():
                        logger.info(f"YOLOv5s公車候選: 信心值 {det['confidence']:.3f}")
                
                # 專門檢測公車
                bus_detections = vehicle_detections[vehicle_detections['class'] == bus_class]
                
                # 先不過濾置信度，看看有沒有任何公車檢測
                if len(bus_detections) > 0:
                    logger.info(f"YOLOv5s檢測到公車數量: {len(bus_detections)}")
                    for _, det in bus_detections.iterrows():
                        logger.info(f"YOLOv5s公車: 信心值 {det['confidence']:.3f}")
                
                # 過濾置信度過低的檢測 - 極低閾值測試
                bus_detections = bus_detections[bus_detections['confidence'] >= 0.3]  # 更低閾值
                if len(bus_detections) > 0:
                    logger.info(f"YOLOv5s過濾後公車數量: {len(bus_detections)} (置信度 >= 0.3)")
                
                for _, detection in bus_detections.iterrows():
                    confidence = detection['confidence']
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    all_bus_bboxes.append(bbox + [confidence])
                    logger.info(f"YOLOv5s公車檢測: 位置({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]}) 信心值:{confidence:.3f}")
            else:
                if frame_count % 30 == 1:
                    logger.info("未啟用車輛檢測模型（無車輛ROI區域）")

            # 處理人頭ROI區域
            region_results = []
            total_count = 0

            if roi_regions and len(roi_regions) > 0:
                for region in roi_regions:
                    if region['closed'] and len(region['points']) > 2:
                        region_bboxes = self._filter_detections_by_region(all_head_bboxes, region['points'])
                        region_count = len(region_bboxes)
                        total_count += region_count

                        region_result = {
                            'region_id': region['id'],
                            'region_name': region['name'],
                            'region_type': 'person',
                            'count': region_count,
                            'bboxes': region_bboxes,
                            'points': region['points']
                        }
                        region_results.append(region_result)
            else:
                # 如果沒有ROI區域，使用所有檢測結果
                total_count = len(all_head_bboxes)
                region_results.append({
                    'region_id': -1,
                    'region_name': 'Full_Frame_Person',
                    'region_type': 'person',
                    'count': total_count,
                    'bboxes': all_head_bboxes,
                    'points': []
                })

            # 處理公車ROI區域
            bus_results = []
            bus_total_count = 0

            if bus_regions and len(bus_regions) > 0:
                logger.info(f"處理 {len(bus_regions)} 個公車區域，檢測到 {len(all_bus_bboxes)} 個車輛")
                for region in bus_regions:
                    if region.get('closed', True) and len(region['points']) > 2:
                        region_bboxes = self._filter_detections_by_region(all_bus_bboxes, region['points'])
                        region_count = len(region_bboxes)
                        bus_total_count += region_count

                        bus_result = {
                            'region_id': region['id'],
                            'region_name': region['name'],
                            'region_type': 'bus',
                            'count': region_count,
                            'bboxes': region_bboxes,
                            'points': region['points']
                        }
                        bus_results.append(bus_result)
                        logger.info(f"公車區域 {region['name']}: {region_count} 輛車")
            else:
                # 如果沒有公車ROI區域，使用所有公車檢測結果
                bus_total_count = len(all_bus_bboxes)
                if bus_total_count > 0:
                    logger.info(f"無公車ROI區域，全幀檢測到 {bus_total_count} 輛車")
                    bus_results.append({
                        'region_id': -1,
                        'region_name': 'Full_Frame_Vehicle',
                        'region_type': 'bus',
                        'count': bus_total_count,
                        'bboxes': all_bus_bboxes,
                        'points': []
                    })

            # 繪製檢測結果
            annotated_frame = self._draw_multi_region_results(annotated_frame, region_results, total_count, bus_results, bus_total_count)

            return {
                'total_count': total_count,
                'region_results': region_results,
                'bus_count': bus_total_count,
                'bus_results': bus_results,
                'annotated_frame': annotated_frame
            }

        except Exception as e:
            logger.error(f"處理多區域檢測結果時出錯: {e}")
            return {
                'total_count': 0,
                'region_results': [],
                'bus_count': 0,
                'bus_results': [],
                'annotated_frame': frame
            }

    def _filter_detections_by_region(self, head_bboxes, region_points):
        """根據區域過濾檢測結果"""
        filtered_bboxes = []
        roi_np = np.array(region_points, dtype=np.int32)

        for bbox in head_bboxes:
            x1, y1, x2, y2, confidence = bbox
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                filtered_bboxes.append(bbox)

        return filtered_bboxes

    def _draw_multi_region_results(self, frame, region_results, total_count, bus_results=None, bus_total_count=0):
        """繪製多區域檢測結果（包含人頭和公車）"""
        # 定義不同區域的顏色
        person_colors = [
            (0, 0, 255),    # 紅色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (0, 255, 255),  # 黃色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]
        
        # 公車使用不同的顏色系列（藍色系列，更粗的線條）
        bus_colors = [
            (255, 0, 0),    # 藍色
            (255, 100, 0),  # 深藍色
            (255, 150, 0),  # 中藍色
            (255, 200, 0),  # 淺藍色
            (255, 50, 50),  # 藍紫色
            (255, 0, 100),  # 深藍紫色
        ]

        global_head_id = 1
        global_bus_id = 1

        # 繪製人頭檢測結果
        for i, region_result in enumerate(region_results):
            color = person_colors[i % len(person_colors)]

            # 繪製該區域的檢測框（更小的正方形框）
            for bbox in region_result['bboxes']:
                x1, y1, x2, y2, confidence = bbox
                
                # 計算中心點
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                
                # 創建小正方形框（15x15像素）
                box_size = 15
                half_size = box_size // 2
                x1_small = center_x - half_size
                y1_small = center_y - half_size
                x2_small = center_x + half_size
                y2_small = center_y + half_size
                
                cv2.rectangle(frame, (x1_small, y1_small), (x2_small, y2_small), color, 1)

                # ID 標籤 - 更小的字體和標籤框
                label = f'P{global_head_id}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.25, 1)[0]
                cv2.rectangle(frame, (x1_small, y1_small - label_size[1] - 2),
                            (x1_small + label_size[0] + 2, y1_small), color, -1)
                cv2.putText(frame, label, (x1_small + 1, y1_small - 1),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.25, (255, 255, 255), 1)

                # 信心值 - 更小字體
                conf_text = f'{confidence:.2f}'
                cv2.putText(frame, conf_text, (x1_small + 1, y1_small - label_size[1] - 4),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.2, (255, 255, 255), 1)

                # 中心點 - 更小
                cv2.circle(frame, (center_x, center_y), 1, color, -1)

                global_head_id += 1

        # 繪製公車檢測結果
        if bus_results:
            for i, bus_result in enumerate(bus_results):
                color = bus_colors[i % len(bus_colors)]

                # 繪製公車檢測框（更小的正方形框）
                for bbox in bus_result['bboxes']:
                    x1, y1, x2, y2, confidence = bbox
                    
                    # 計算中心點
                    center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                    
                    # 創建小正方形框（20x20像素，比人頭稍大）
                    box_size = 20
                    half_size = box_size // 2
                    x1_small = center_x - half_size
                    y1_small = center_y - half_size
                    x2_small = center_x + half_size
                    y2_small = center_y + half_size
                    
                    cv2.rectangle(frame, (x1_small, y1_small), (x2_small, y2_small), color, 2)

                    # ID 標籤 - 顯示公車類型
                    label = f'B{global_bus_id}'  # B for Bus
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.3, 1)[0]
                    cv2.rectangle(frame, (x1_small, y1_small - label_size[1] - 3),
                                (x1_small + label_size[0] + 3, y1_small), color, -1)
                    cv2.putText(frame, label, (x1_small + 1, y1_small - 1),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)

                    # 信心值 - 更小字體
                    conf_text = f'{confidence:.2f}'
                    cv2.putText(frame, conf_text, (x1_small + 1, y1_small - label_size[1] - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.25, (255, 255, 255), 1)

                    # 中心點 - 更小
                    cv2.circle(frame, (center_x, center_y), 2, color, -1)

                    global_bus_id += 1

        # 顯示總計數（左上角）
        total_text = f'Person Count: {total_count}'
        cv2.putText(frame, total_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
        
        # 顯示公車總計數
        if bus_total_count > 0:
            bus_text = f'Bus Count: {bus_total_count}'
            cv2.putText(frame, bus_text, (10, 65),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 3)
            y_offset = 100
        else:
            y_offset = 70

        # 顯示各人頭區域計數
        for region_result in region_results:
            if region_result['region_id'] >= 0:  # 跳過全幀結果
                region_text = f"{region_result['region_name']}: {region_result['count']}"
                cv2.putText(frame, region_text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
                y_offset += 25

        # 顯示各公車區域計數
        if bus_results:
            for bus_result in bus_results:
                if bus_result['region_id'] >= 0:  # 跳過全幀結果
                    bus_region_text = f"{bus_result['region_name']}: {bus_result['count']} buses"
                    cv2.putText(frame, bus_region_text, (10, y_offset),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    y_offset += 25

        # 模式和GPU信息
        mode_text = f'Mode: {self.model_type.upper()} + YOLOv5s (GPU) - Multi-Region + Bus'
        cv2.putText(frame, mode_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)

        if self.device == 'cuda':
            gpu_memory = torch.cuda.memory_allocated() / 1024**2
            gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
            cv2.putText(frame, gpu_text, (10, y_offset + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 時間戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cv2.putText(frame, timestamp, (10, frame.shape[0] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return frame

    def _process_single_frame_result(self, frame, results, roi_points=None):
        """处理单帧检测结果"""
        try:
            detections = results.pandas().xyxy[0]
            head_bboxes = []
            annotated_frame = frame.copy()
            
            if self.model_type == 'custom':
                target_detections = detections[detections['name'].isin(['head', 'person'])]
            else:
                # 0: person, 25: umbrella
                target_detections = detections[detections['class'].isin([0, 25])]

            for _, detection in target_detections.iterrows():
                confidence = detection['confidence']
                if self.model_type == 'custom' and detection['name'] == 'head':
                    bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                else: # Person or Umbrella
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])]
                    bbox = self._extract_head_region(frame, person_bbox)
                head_bboxes.append(bbox + [confidence])

            # 如果定义了ROI，则过滤检测结果
            if roi_points and len(roi_points) > 2:
                filtered_bboxes = []
                roi_np = np.array(roi_points, dtype=np.int32)
                for bbox in head_bboxes:
                    x1, y1, x2, y2, _ = bbox
                    center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                    if cv2.pointPolygonTest(roi_np, (center_x, center_y), False) >= 0:
                        filtered_bboxes.append(bbox)
                head_bboxes = filtered_bboxes

            # 绘制检测结果
            head_count = len(head_bboxes)
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
                label = f'{i+1}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                # ID 標籤底色
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6), 
                            (x1 + label_size[0] + 4, y1), (0, 0, 255), -1)
                # ID 文字
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                # 信心值（白色小字）
                conf_text = f'{confidence:.2f}'
                conf_size = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, 0.35, 1)[0]
                conf_y = y1 - label_size[1] - 8  # 再往上
                cv2.putText(annotated_frame, conf_text, (x1 + 2, conf_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, (255, 0, 0), -1)
            
            count_text = f'Person Count: {head_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            mode_text = f'Mode: {self.model_type.upper()} (GPU)'
            cv2.putText(annotated_frame, mode_text, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            if self.device == 'cuda':
                gpu_memory = torch.cuda.memory_allocated() / 1024**2
                gpu_text = f'GPU Memory: {gpu_memory:.0f}MB'
                cv2.putText(annotated_frame, gpu_text, (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"处理帧结果时出错: {e}")
            return 0, [], frame

class GPUOptimizedRTSPProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='gpu_head_detection.mp4', detection_interval=1, 
                 video_quality='medium', target_fps=None, target_resolution=None):
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        self.frame_times = []
        self.gpu_memory_usage = []
        
        # 視頻優化設定
        self.video_quality = video_quality
        self.target_fps = target_fps
        self.target_resolution = target_resolution
        self.original_fps = None
        self.original_width = None
        self.original_height = None

        # 檢測間隔控制
        self.detection_interval = detection_interval
        self.last_detection_result = None  # 緩存上次檢測結果
        self.detection_frame_count = 0  # 檢測幀計數器

        # 多區域 ROI 设置
        self.roi_file = "multi_roi_regions.json"
        self.roi_regions = self._load_multi_roi()  # 存儲多個區域
        self.current_region_points = []  # 當前正在繪製的區域點
        self.current_region_closed = False  # 當前區域是否已關閉
        self.selected_region_index = -1  # 當前選中的區域索引（用於編輯）
        self.selected_region_type = 'person'  # 當前選中區域的類型
        self.window_name = 'GPU_Head_Detection'
        
        # 編輯模式設置
        self.edit_mode = False  # 是否處於編輯模式
        self.edit_point_index = -1  # 正在編輯的點索引
        self.drag_mode = False  # 是否處於拖拽模式
        self.edit_threshold = 10  # 點選擇的閾值（像素）
        
        # 公車檢測區域設置
        self.current_region_type = 'person'  # 當前繪製的區域類型：'person' 或 'bus'
        # self.bus_regions 已在 _load_multi_roi() 中初始化，不要重複初始化
        self.bus_statistics = {}  # 公車統計數據

        # 統計數據
        self.region_statistics = {}  # 每個區域的統計數據
        self.total_statistics = {
            'total_count': 0,
            'region_counts': [],
            'detection_history': []
        }

        logger.info(f"檢測間隔設置為: 每 {self.detection_interval} 幀檢測一次")
        
        # 顯示視頻優化設定
        self._log_video_optimization_settings()
        
        # 啟動時顯示載入的區域信息
        if len(self.roi_regions) > 0 or len(self.bus_regions) > 0:
            logger.info(f"✅ 成功載入 {len(self.roi_regions)} 個人頭檢測區域和 {len(self.bus_regions)} 個公車檢測區域")
            for i, region in enumerate(self.roi_regions):
                logger.info(f"   人頭區域 {i+1}: {region.get('name', f'Person_Region_{i+1}')} ({len(region['points'])} 個點)")
            for i, region in enumerate(self.bus_regions):
                logger.info(f"   公車區域 {i+1}: {region.get('name', f'Bus_Region_{i+1}')} ({len(region['points'])} 個點)")
        else:
            logger.info("⚠️  未找到已保存的檢測區域，請手動繪製ROI區域")

    def _log_video_optimization_settings(self):
        """顯示視頻優化設定"""
        if self.save_video:
            logger.info("=== 視頻優化設定 ===")
            logger.info(f"品質設定: {self.video_quality}")
            if self.target_fps:
                logger.info(f"目標FPS: {self.target_fps}")
            if self.target_resolution:
                logger.info(f"目標解析度: {self.target_resolution[0]}x{self.target_resolution[1]}")
            logger.info(f"輸出路徑: {self.output_path}")

    def _get_optimized_video_settings(self, original_fps, original_width, original_height):
        """獲取優化的視頻設定"""
        quality_settings = {
            'high': {
                'fourcc': 'H264',
                'fps_factor': 1.0,
                'resolution_factor': 1.0,
                'description': '高品質 - 檔案較大，適合存檔'
            },
            'medium': {
                'fourcc': 'H264', 
                'fps_factor': 0.83,  # 25fps (from 30fps)
                'resolution_factor': 1.0,
                'description': '中等品質 - 平衡檔案大小和品質'
            },
            'low': {
                'fourcc': 'H264',
                'fps_factor': 0.67,  # 20fps
                'resolution_factor': 0.85,  # 約減少30%檔案大小
                'description': '低品質 - 檔案較小，適合長時間錄影'
            },
            'very_low': {
                'fourcc': 'X264',
                'fps_factor': 0.5,   # 15fps
                'resolution_factor': 0.75,  # 約減少45%檔案大小
                'description': '很低品質 - 檔案很小，適合監控存檔'
            }
        }
        
        settings = quality_settings.get(self.video_quality, quality_settings['medium'])
        
        # 計算優化後的FPS
        if self.target_fps:
            optimized_fps = self.target_fps
        else:
            optimized_fps = max(10, int(original_fps * settings['fps_factor']))
        
        # 計算優化後的解析度
        if self.target_resolution:
            optimized_width, optimized_height = self.target_resolution
        else:
            optimized_width = int(original_width * settings['resolution_factor'])
            optimized_height = int(original_height * settings['resolution_factor'])
        
        # 確保解析度是偶數（H.264要求）
        optimized_width = optimized_width if optimized_width % 2 == 0 else optimized_width - 1
        optimized_height = optimized_height if optimized_height % 2 == 0 else optimized_height - 1
        
        # 估算檔案大小減少比例
        size_reduction = 1 - (optimized_width * optimized_height * optimized_fps) / (original_width * original_height * original_fps)
        
        logger.info(f"視頻優化: {settings['description']}")
        logger.info(f"  原始設定: {original_width}x{original_height} @ {original_fps} FPS")
        logger.info(f"  優化設定: {optimized_width}x{optimized_height} @ {optimized_fps} FPS")
        logger.info(f"  編碼格式: {settings['fourcc']}")
        logger.info(f"  預估檔案大小減少: {size_reduction*100:.1f}%")
        
        return settings['fourcc'], optimized_fps, optimized_width, optimized_height

    def _load_multi_roi(self):
        """從文件加載多個ROI區域"""
        # 初始化公車區域
        self.bus_regions = []
        
        if os.path.exists(self.roi_file):
            try:
                with open(self.roi_file, 'r') as f:
                    data = json.load(f)
                    # 兼容舊格式：如果是點列表，轉換為新格式
                    if isinstance(data, list) and len(data) > 0 and isinstance(data[0], list):
                        regions = [{'id': 0, 'name': 'Region_1', 'points': data, 'closed': True}]
                        self.bus_regions = []
                        logger.info(f"轉換舊格式ROI到新的多區域格式")
                    else:
                        regions = data.get('regions', [])
                        self.bus_regions = data.get('bus_regions', [])
                    logger.info(f"成功從 {self.roi_file} 加載 {len(regions)} 個人頭區域和 {len(self.bus_regions)} 個公車區域")
                    return regions
            except Exception as e:
                logger.error(f"加載ROI文件失敗: {e}")
        
        return []

    def _save_multi_roi(self):
        """保存多個ROI區域到文件"""
        try:
            data = {
                'regions': self.roi_regions,
                'bus_regions': self.bus_regions,
                'created_at': datetime.now().isoformat(),
                'total_regions': len(self.roi_regions),
                'total_bus_regions': len(self.bus_regions)
            }
            with open(self.roi_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"✅ 已保存 {len(self.roi_regions)} 個人頭區域和 {len(self.bus_regions)} 個公車區域到 {self.roi_file}")
            logger.info(f"   文件路徑: {os.path.abspath(self.roi_file)}")
        except Exception as e:
            logger.error(f"保存ROI文件失敗: {e}")

    def _add_new_region(self):
        """添加新的ROI區域"""
        if len(self.current_region_points) > 2:
            if self.current_region_type == 'person':
                region_id = len(self.roi_regions)
                region_name = f"Person_Region_{region_id + 1}"
                new_region = {
                    'id': region_id,
                    'name': region_name,
                    'type': 'person',
                    'points': self.current_region_points.copy(),
                    'closed': True,
                    'created_at': datetime.now().isoformat()
                }
                self.roi_regions.append(new_region)
                logger.info(f"添加新人頭區域: {region_name} (共 {len(self.roi_regions)} 個人頭區域)")
                self._save_multi_roi()  # 確保立即保存
                logger.info(f"人頭區域已保存到文件: {self.roi_file}")
            else:  # bus
                region_id = len(self.bus_regions)
                region_name = f"Bus_Region_{region_id + 1}"
                new_region = {
                    'id': region_id,
                    'name': region_name,
                    'type': 'bus',
                    'points': self.current_region_points.copy(),
                    'closed': True,
                    'created_at': datetime.now().isoformat()
                }
                self.bus_regions.append(new_region)
                logger.info(f"添加新公車區域: {region_name} (共 {len(self.bus_regions)} 個公車區域)")
                self._save_multi_roi()  # 確保立即保存
                logger.info(f"公車區域已保存到文件: {self.roi_file}")
            
            self.current_region_points = []
            self.current_region_closed = False
            return region_id
        return -1

    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數用於繪製多個ROI區域（增強編輯功能）"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 檢查是否在編輯模式
            if self.edit_mode and self.selected_region_index >= 0:
                self._handle_edit_mode_click(x, y)
                return
            
            # 左鍵點擊：添加點到當前區域
            if self.current_region_closed:
                logger.warning("當前區域已關閉，請按 'n' 開始新人頭區域或 'b' 開始新公車區域")
                return
            self.current_region_points.append([x, y])
            logger.info(f"添加ROI點到當前{self.current_region_type}區域: ({x}, {y}) - 點數: {len(self.current_region_points)}")

        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右鍵點擊：關閉當前區域或退出編輯模式
            if self.edit_mode:
                self._exit_edit_mode()
                return
                
            if not self.current_region_closed and len(self.current_region_points) > 2:
                region_id = self._add_new_region()
                if region_id >= 0:
                    self.current_region_closed = True
                    region_type = self.current_region_type
                    logger.info(f"✅ {region_type}區域 {region_id + 1} 已關閉並自動保存到 {self.roi_file}")
                    # 強制保存確認
                    self._save_multi_roi()
                else:
                    logger.warning("區域點數不足，無法關閉")

        elif event == cv2.EVENT_MBUTTONDOWN:
            # 中鍵點擊：選擇/取消選擇區域（用於編輯）
            self._select_region_at_point(x, y)
            
        elif event == cv2.EVENT_LBUTTONDBLCLK:
            # 雙擊左鍵：進入編輯模式
            if not self.edit_mode:
                self._enter_edit_mode_at_point(x, y)
                
        elif event == cv2.EVENT_MOUSEMOVE:
            # 鼠標移動：處理拖拽
            if self.edit_mode and self.drag_mode:
                self._handle_mouse_drag(x, y)
                
        elif event == cv2.EVENT_LBUTTONUP:
            # 左鍵釋放：結束拖拽
            if self.edit_mode and self.drag_mode:
                self.drag_mode = False
                self._save_multi_roi()  # 保存拖拽結果
                logger.info("點移動完成，已保存")

    def _select_region_at_point(self, x, y):
        """選擇指定點所在的區域（支持人頭和公車區域，擴大選擇範圍）"""
        # 擴大選擇範圍：檢查點是否在區域邊界附近
        selection_tolerance = 20  # 增加容差到20像素
        
        logger.info(f"🖱️ 中鍵點擊位置: ({x}, {y})")
        
        # 收集所有可能的候選區域
        candidates = []
        
        # 檢查人頭區域
        for i, region in enumerate(self.roi_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)
                
                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('person', i, region, 0))  # 0表示在內部，優先級最高
                    continue
                
                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('person', i, region, distance))
        
        # 檢查公車區域
        for i, region in enumerate(self.bus_regions):
            if len(region['points']) > 2:
                roi_np = np.array(region['points'], dtype=np.int32)
                
                # 檢查點是否在區域內部
                if cv2.pointPolygonTest(roi_np, (x, y), False) >= 0:
                    candidates.append(('bus', i, region, 0))  # 0表示在內部，優先級最高
                    continue
                
                # 檢查點是否在區域邊界附近
                distance = abs(cv2.pointPolygonTest(roi_np, (x, y), True))
                if distance <= selection_tolerance:
                    candidates.append(('bus', i, region, distance))
        
        if candidates:
            # 按距離排序，優先選擇距離最近的（內部點距離為0）
            candidates.sort(key=lambda x: x[3])
            
            # 如果有多個候選，優先選擇公車區域（因為通常比較難選中）
            bus_candidates = [c for c in candidates if c[0] == 'bus' and c[3] == 0]
            if bus_candidates:
                region_type, region_index, region, distance = bus_candidates[0]
            else:
                region_type, region_index, region, distance = candidates[0]
            
            self.selected_region_index = region_index
            self.selected_region_type = region_type
            
            region_name = region.get('name', f'{region_type.title()}_Region_{region_index + 1}')
            distance_info = "(內部)" if distance == 0 else f"(邊界附近 {distance:.1f}px)"
            logger.info(f"✅ 選中{region_type}區域 {region_index + 1}: {region_name} {distance_info}")
            
            # 顯示所有候選區域信息
            if len(candidates) > 1:
                logger.info(f"   發現 {len(candidates)} 個候選區域，已選擇最佳匹配")
                for i, (rtype, ridx, reg, dist) in enumerate(candidates):
                    marker = "👉" if i == 0 else "  "
                    logger.info(f"   {marker} {rtype}區域 {ridx + 1}: 距離 {dist:.1f}px")
            
            return
        
        self.selected_region_index = -1
        self.selected_region_type = 'person'
        logger.info("❌ 未選中任何區域 - 請點擊區域內部或邊界附近")
        logger.info(f"   提示: 容差範圍為 {selection_tolerance} 像素，或使用數字鍵快速選擇")

    def _select_region_by_number(self, region_num):
        """使用數字鍵快速選擇區域"""
        logger.info(f"🔍 嘗試選擇區域 {region_num + 1}")
        logger.info(f"   人頭區域數: {len(self.roi_regions)}, 公車區域數: {len(self.bus_regions)}")
        
        # 先檢查人頭區域
        if region_num < len(self.roi_regions):
            self.selected_region_index = region_num
            self.selected_region_type = 'person'
            region_name = self.roi_regions[region_num].get('name', f'Person_Region_{region_num + 1}')
            logger.info(f"✅ 快速選中人頭區域 {region_num + 1}: {region_name}")
            logger.info(f"   設置: selected_region_index={self.selected_region_index}, type={self.selected_region_type}")
            return
        
        # 再檢查公車區域
        bus_index = region_num - len(self.roi_regions)
        logger.info(f"   計算公車索引: {region_num} - {len(self.roi_regions)} = {bus_index}")
        
        if bus_index >= 0 and bus_index < len(self.bus_regions):
            self.selected_region_index = bus_index  # 這裡應該是bus_index，不是region_num
            self.selected_region_type = 'bus'
            region_name = self.bus_regions[bus_index].get('name', f'Bus_Region_{bus_index + 1}')
            logger.info(f"✅ 快速選中公車區域 {bus_index + 1}: {region_name}")
            logger.info(f"   設置: selected_region_index={self.selected_region_index}, type={self.selected_region_type}")
            return
        
        # 沒有找到對應的區域
        total_regions = len(self.roi_regions) + len(self.bus_regions)
        logger.warning(f"❌ 區域 {region_num + 1} 不存在 (總共有 {total_regions} 個區域)")
        logger.info("💡 提示: 按 'i' 查看所有區域信息")
        if len(self.roi_regions) > 0:
            logger.info(f"   人頭區域: 按 1-{len(self.roi_regions)}")
        if len(self.bus_regions) > 0:
            start_num = len(self.roi_regions) + 1
            end_num = len(self.roi_regions) + len(self.bus_regions)
            logger.info(f"   公車區域: 按 {start_num}-{end_num}")

    def _enter_edit_mode_at_point(self, x, y):
        """在指定點進入編輯模式"""
        self._select_region_at_point(x, y)
        if self.selected_region_index >= 0:
            self.edit_mode = True
            self.edit_point_index = -1
            logger.info(f"進入編輯模式 - {self.selected_region_type}區域 {self.selected_region_index + 1}")
            logger.info("編輯模式操作說明:")
            logger.info("  - 左鍵點擊點附近: 選擇並拖拽點")
            logger.info("  - 左鍵點擊邊線: 在該位置插入新點")
            logger.info("  - 右鍵點擊: 退出編輯模式")
            logger.info("  - Delete鍵: 刪除選中的點")
        else:
            logger.warning("未找到可編輯的區域")

    def _exit_edit_mode(self):
        """退出編輯模式"""
        if self.edit_mode:
            self.edit_mode = False
            self.edit_point_index = -1
            self.drag_mode = False
            self._save_multi_roi()  # 保存編輯結果
            logger.info("已退出編輯模式並保存變更")

    def _handle_edit_mode_click(self, x, y):
        """處理編輯模式下的點擊事件"""
        if self.selected_region_index < 0:
            return
            
        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                return
            region = self.bus_regions[self.selected_region_index]
        
        points = region['points']
        
        # 檢查是否點擊了現有的點
        for i, point in enumerate(points):
            distance = np.sqrt((x - point[0])**2 + (y - point[1])**2)
            if distance <= self.edit_threshold:
                self.edit_point_index = i
                self.drag_mode = True
                logger.info(f"選中點 {i + 1}，可拖拽移動")
                return
        
        # 檢查是否點擊了邊線，如果是則插入新點
        self._insert_point_on_edge(x, y, points)

    def _insert_point_on_edge(self, x, y, points):
        """在邊線上插入新點"""
        min_distance = float('inf')
        insert_index = -1
        
        for i in range(len(points)):
            p1 = points[i]
            p2 = points[(i + 1) % len(points)]
            
            # 計算點到線段的距離
            distance = self._point_to_line_distance(x, y, p1, p2)
            
            if distance < self.edit_threshold and distance < min_distance:
                min_distance = distance
                insert_index = i + 1
        
        if insert_index >= 0:
            points.insert(insert_index, [x, y])
            self.edit_point_index = insert_index
            self._save_multi_roi()
            logger.info(f"在位置 {insert_index} 插入新點: ({x}, {y})")

    def _point_to_line_distance(self, px, py, p1, p2):
        """計算點到線段的距離"""
        x1, y1 = p1
        x2, y2 = p2
        
        # 線段長度的平方
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2
        
        if line_length_sq == 0:
            return np.sqrt((px - x1)**2 + (py - y1)**2)
        
        # 計算投影參數
        t = max(0, min(1, ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) / line_length_sq))
        
        # 投影點
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # 返回距離
        return np.sqrt((px - proj_x)**2 + (py - proj_y)**2)

    def _handle_mouse_drag(self, x, y):
        """處理鼠標拖拽事件"""
        if self.edit_mode and self.drag_mode and self.edit_point_index >= 0:
            # 獲取當前選中的區域
            if self.selected_region_type == 'person':
                if self.selected_region_index >= len(self.roi_regions):
                    return
                region = self.roi_regions[self.selected_region_index]
            else:  # bus
                if self.selected_region_index >= len(self.bus_regions):
                    return
                region = self.bus_regions[self.selected_region_index]
            
            # 更新點的位置
            if self.edit_point_index < len(region['points']):
                region['points'][self.edit_point_index] = [x, y]

    def _delete_selected_point(self):
        """刪除選中的點"""
        if not self.edit_mode or self.edit_point_index < 0 or self.selected_region_index < 0:
            logger.warning("沒有選中的點可刪除")
            return
            
        # 獲取當前選中的區域
        if self.selected_region_type == 'person':
            if self.selected_region_index >= len(self.roi_regions):
                return
            region = self.roi_regions[self.selected_region_index]
        else:  # bus
            if self.selected_region_index >= len(self.bus_regions):
                return
            region = self.bus_regions[self.selected_region_index]
        
        points = region['points']
        
        # 確保至少保留3個點
        if len(points) <= 3:
            logger.warning("區域至少需要3個點，無法刪除")
            return
        
        # 刪除點
        if self.edit_point_index < len(points):
            deleted_point = points.pop(self.edit_point_index)
            self.edit_point_index = -1
            self._save_multi_roi()
            logger.info(f"已刪除點: {deleted_point}")

    def _clear_all_roi(self):
        """清除所有ROI區域"""
        self.roi_regions = []
        self.bus_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        self.region_statistics = {}
        self.bus_statistics = {}
        if os.path.exists(self.roi_file):
            os.remove(self.roi_file)
        logger.info("所有ROI區域已清除（包含人頭和公車區域）")

    def _start_new_region(self, region_type='person'):
        """開始繪製新區域"""
        if not self.current_region_closed and len(self.current_region_points) > 0:
            logger.warning("當前區域未完成，請先右鍵關閉當前區域")
            return
        self.current_region_points = []
        self.current_region_closed = False
        self.current_region_type = region_type
        logger.info(f"開始繪製新{region_type}區域")

    def _delete_selected_region(self):
        """刪除選中的區域（支持人頭和公車區域）"""
        if self.selected_region_index < 0:
            logger.warning("沒有選中的區域可刪除")
            return
            
        if self.selected_region_type == 'person':
            if self.selected_region_index < len(self.roi_regions):
                deleted_region = self.roi_regions.pop(self.selected_region_index)
                # 重新分配ID
                for i, region in enumerate(self.roi_regions):
                    region['id'] = i
                    region['name'] = f"Person_Region_{i + 1}"
                logger.info(f"已刪除人頭區域: {deleted_region['name']}")
            else:
                logger.warning("選中的人頭區域索引無效")
        else:  # bus
            if self.selected_region_index < len(self.bus_regions):
                deleted_region = self.bus_regions.pop(self.selected_region_index)
                # 重新分配ID
                for i, region in enumerate(self.bus_regions):
                    region['id'] = i
                    region['name'] = f"Bus_Region_{i + 1}"
                logger.info(f"已刪除公車區域: {deleted_region['name']}")
            else:
                logger.warning("選中的公車區域索引無效")
        
        self.selected_region_index = -1
        self.selected_region_type = 'person'
        self.edit_mode = False
        self.edit_point_index = -1
        self.drag_mode = False
        self._save_multi_roi()

    def _update_statistics(self, region_results, total_count):
        """更新統計數據"""
        # 更新總統計
        self.total_statistics['total_count'] = total_count
        self.total_statistics['region_counts'] = [r['count'] for r in region_results]
        self.total_statistics['detection_history'].append({
            'timestamp': datetime.now().isoformat(),
            'total_count': total_count,
            'region_counts': {r['region_name']: r['count'] for r in region_results}
        })

        # 保持歷史記錄在合理範圍內
        if len(self.total_statistics['detection_history']) > 1000:
            self.total_statistics['detection_history'] = self.total_statistics['detection_history'][-1000:]

        # 更新各區域統計
        for region_result in region_results:
            region_name = region_result['region_name']
            if region_name not in self.region_statistics:
                self.region_statistics[region_name] = {
                    'total_detections': 0,
                    'max_count': 0,
                    'avg_count': 0,
                    'detection_count': 0
                }

            stats = self.region_statistics[region_name]
            stats['total_detections'] += region_result['count']
            stats['max_count'] = max(stats['max_count'], region_result['count'])
            stats['detection_count'] += 1
            stats['avg_count'] = stats['total_detections'] / stats['detection_count']

    def _update_bus_statistics(self, bus_results, bus_total_count):
        """更新公車統計數據"""
        # 更新公車統計
        for bus_result in bus_results:
            region_name = bus_result['region_name']
            if region_name not in self.bus_statistics:
                self.bus_statistics[region_name] = {
                    'total_detections': 0,
                    'max_count': 0,
                    'avg_count': 0,
                    'detection_count': 0
                }

            stats = self.bus_statistics[region_name]
            stats['total_detections'] += bus_result['count']
            stats['max_count'] = max(stats['max_count'], bus_result['count'])
            stats['detection_count'] += 1
            stats['avg_count'] = stats['total_detections'] / stats['detection_count']

    def _get_statistics_summary(self):
        """獲取統計摘要"""
        summary = {
            'total_regions': len(self.roi_regions),
            'current_total': self.total_statistics['total_count'],
            'region_stats': self.region_statistics.copy()
        }
        return summary

    def _draw_roi_regions(self, frame):
        """繪製所有ROI區域"""
        # 定義人頭區域的顏色
        person_colors = [
            (0, 255, 255),  # 黃色
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (255, 0, 255),  # 洋紅
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 橙藍色
        ]
        
        # 定義公車區域的顏色（藍色系列）
        bus_colors = [
            (255, 0, 0),    # 藍色
            (255, 100, 0),  # 深藍色
            (255, 150, 0),  # 中藍色
            (255, 200, 0),  # 淺藍色
            (255, 50, 50),  # 藍紫色
            (255, 0, 100),  # 深藍紫色
        ]

        # 繪製已完成的人頭區域
        for i, region in enumerate(self.roi_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                color = person_colors[i % len(person_colors)]
                roi_np = np.array([region['points']], dtype=np.int32)

                # 如果是選中的區域，使用稍粗的線條，否則使用細線
                is_selected = (i == self.selected_region_index and self.selected_region_type == 'person')
                thickness = 3 if is_selected else 1
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)

                # 如果處於編輯模式且是選中的區域，繪製編輯點
                if self.edit_mode and is_selected:
                    self._draw_edit_points(frame, region['points'], color)

                # 添加區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    region_name = region.get('name', f'Person_Region_{i+1}')
                    label_text = f"{region_name} [EDIT]" if (self.edit_mode and is_selected) else region_name
                    cv2.putText(frame, label_text,
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        # 繪製已完成的公車區域
        for i, region in enumerate(self.bus_regions):
            if region.get('closed', True) and len(region['points']) > 2:
                color = bus_colors[i % len(bus_colors)]
                roi_np = np.array([region['points']], dtype=np.int32)

                # 公車區域線條，選中時稍粗，否則使用細線
                is_selected = (i == self.selected_region_index and self.selected_region_type == 'bus')
                thickness = 3 if is_selected else 1
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)

                # 如果處於編輯模式且是選中的區域，繪製編輯點
                if self.edit_mode and is_selected:
                    self._draw_edit_points(frame, region['points'], color)

                # 添加區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    region_name = region.get('name', f'Bus_Region_{i+1}')
                    label_text = f"{region_name} [EDIT]" if (self.edit_mode and is_selected) else region_name
                    cv2.putText(frame, label_text,
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # 繪製當前正在繪製的區域
        if len(self.current_region_points) > 0:
            # 根據當前區域類型選擇顏色
            if self.current_region_type == 'bus':
                current_color = (255, 0, 0)  # 藍色表示正在繪製公車區域
                region_text = f"Drawing Bus Region: {len(self.current_region_points)} points"
            else:
                current_color = (0, 0, 255)  # 紅色表示正在繪製人頭區域
                region_text = f"Drawing Person Region: {len(self.current_region_points)} points"

            if len(self.current_region_points) > 1:
                roi_np = np.array([self.current_region_points], dtype=np.int32)
                thickness = 1  # 統一使用細線
                cv2.polylines(frame, roi_np, isClosed=False, color=current_color, thickness=thickness)

            # 繪製所有點
            point_radius = 6 if self.current_region_type == 'bus' else 5
            for point in self.current_region_points:
                cv2.circle(frame, tuple(point), point_radius, current_color, -1)

            # 顯示當前點數和區域類型
            if len(self.current_region_points) > 0:
                cv2.putText(frame, region_text, (10, frame.shape[0] - 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, current_color, 2)

    def _draw_edit_points(self, frame, points, color):
        """繪製編輯模式下的控制點"""
        for i, point in enumerate(points):
            x, y = point
            
            # 繪製控制點
            if i == self.edit_point_index:
                # 選中的點用較大的圓圈和不同顏色
                cv2.circle(frame, (x, y), 8, (0, 255, 255), 2)  # 黃色圓圈
                cv2.circle(frame, (x, y), 4, (0, 0, 255), -1)   # 紅色實心
            else:
                # 普通控制點
                cv2.circle(frame, (x, y), 6, color, 2)
                cv2.circle(frame, (x, y), 3, (255, 255, 255), -1)
            
            # 顯示點的編號
            cv2.putText(frame, str(i + 1), (x + 10, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 顯示編輯模式提示
        if self.edit_mode:
            edit_info = f"Edit Mode: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
            if self.edit_point_index >= 0:
                edit_info += f" | Selected Point: {self.edit_point_index + 1}"
            cv2.putText(frame, edit_info, (10, frame.shape[0] - 80),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

    def _draw_status_info(self, frame):
        """繪製當前狀態信息"""
        # 左下角顯示編輯模式狀態（大字體，醒目）
        if self.edit_mode:
            edit_text = "EDIT MODE"
            edit_color = (0, 255, 255)  # 黃色表示編輯模式
            # 左下角位置
            edit_y = frame.shape[0] - 30
            cv2.putText(frame, edit_text, (10, edit_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, edit_color, 3)
            
            # 編輯模式詳細信息
            if self.selected_region_index >= 0:
                detail_text = f"Editing: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
                cv2.putText(frame, detail_text, (10, edit_y - 35), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, edit_color, 2)
                
                if self.edit_point_index >= 0:
                    point_text = f"Selected Point: {self.edit_point_index + 1}"
                    cv2.putText(frame, point_text, (10, edit_y - 65), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, edit_color, 2)
        
        # 右下角顯示其他狀態信息
        y_start = frame.shape[0] - 150
        x_right = frame.shape[1] - 400  # 右側位置
        
        # 顯示選中區域狀態
        if self.selected_region_index >= 0:
            status_text = f"Selected: {self.selected_region_type.upper()} Region {self.selected_region_index + 1}"
            color = (0, 255, 0)  # 綠色表示已選中
        else:
            status_text = "No Region Selected (Middle-click to select)"
            color = (0, 0, 255)  # 紅色表示未選中
        
        cv2.putText(frame, status_text, (x_right, y_start), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 顯示編輯模式提示（非編輯模式時）
        if not self.edit_mode:
            edit_hint = "Press 'e' to enter EDIT MODE"
            cv2.putText(frame, edit_hint, (x_right, y_start + 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (128, 128, 128), 2)
        else:
            exit_hint = "Press 'e' or ESC to exit EDIT MODE"
            cv2.putText(frame, exit_hint, (x_right, y_start + 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 顯示快捷鍵提示
        shortcuts = [
            "Shortcuts: 'e'=Edit, '1-9'=Select Region, 'n'=New Person, 'b'=New Bus, 'd'=Delete, 'h'=Help"
        ]
        
        cv2.putText(frame, shortcuts[0], (10, y_start + 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 顯示區域編號提示
        if len(self.roi_regions) > 0 or len(self.bus_regions) > 0:
            region_info = f"Regions: Person(1-{len(self.roi_regions)})"
            if len(self.bus_regions) > 0:
                start_num = len(self.roi_regions) + 1
                end_num = len(self.roi_regions) + len(self.bus_regions)
                region_info += f", Bus({start_num}-{end_num})"
            
            cv2.putText(frame, region_info, (10, y_start + 75), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

    def _print_region_info(self):
        """打印區域信息"""
        logger.info("=== 多區域ROI信息 ===")
        logger.info(f"人頭區域數: {len(self.roi_regions)}")
        logger.info(f"公車區域數: {len(self.bus_regions)}")

        for i, region in enumerate(self.roi_regions):
            status = "選中" if i == self.selected_region_index else "未選中"
            logger.info(f"人頭區域 {i+1}: {region.get('name', f'Person_Region_{i+1}')} - 點數: {len(region['points'])} - 狀態: {status}")

        for i, region in enumerate(self.bus_regions):
            logger.info(f"公車區域 {i+1}: {region.get('name', f'Bus_Region_{i+1}')} - 點數: {len(region['points'])}")

        if len(self.current_region_points) > 0:
            logger.info(f"正在繪製 {self.current_region_type} 區域: {len(self.current_region_points)} 個點")

        # 顯示統計信息
        stats = self._get_statistics_summary()
        logger.info(f"當前總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"  {region_name}: 平均 {region_stats['avg_count']:.1f}, "
                       f"最大 {region_stats['max_count']}, "
                       f"檢測次數 {region_stats['detection_count']}")

    def _print_help(self):
        """打印幫助信息"""
        help_text = f"""
=== 多區域頭部和公車檢測控制說明（增強編輯版） ===
🖱️ 鼠標操作:
  左鍵點擊: 添加點到當前區域 / 編輯模式下選擇並拖拽點
  右鍵點擊: 完成當前區域繪製 / 退出編輯模式
  中鍵點擊: 選擇/取消選擇區域
  雙擊左鍵: 進入編輯模式（在區域內雙擊）
  拖拽: 編輯模式下移動選中的點

⌨️ 鍵盤操作:
  'q': 退出程序
  'c': 清除所有ROI區域
  'n': 開始繪製新人頭區域
  'b': 開始繪製新公車區域 ⭐NEW⭐
  'd': 刪除選中的區域 / 編輯模式下刪除選中的點
  'e': 進入/退出編輯模式（需先選中區域）
  '1-9': 快速選擇區域（按數字鍵） ⭐NEW⭐
  'ESC': 退出編輯模式
  's': 保存當前幀
  'i': 顯示區域信息
  'h': 顯示此幫助信息

📝 區域繪製流程:
1. 按 'n' 開始新人頭區域 或 按 'b' 開始新公車區域
2. 左鍵點擊添加點 (至少3個點)
3. 右鍵點擊完成區域並自動保存
4. 重複步驟1-3添加更多區域

✏️ 區域編輯流程:
1. 中鍵點擊選擇要編輯的區域
2. 雙擊左鍵或按 'e' 進入編輯模式
3. 左鍵點擊點附近選擇並拖拽移動
4. 左鍵點擊邊線插入新點
5. 按 'd' 刪除選中的點
6. 右鍵或ESC退出編輯模式

🔍 檢測說明:
- 人頭檢測: 紅色線框，標籤P1, P2... (YOLOv5s模型)
- 公車檢測: 藍色線框，標籤B1, B2... (YOLOv5s模型)
- 選中區域: 較粗線條顯示
- 編輯模式: 顯示控制點和 [EDIT] 標籤
- 左上角顯示: Person Count 和 Bus Count

⚡ 檢測間隔控制:
- 當前間隔: 每 {self.detection_interval} 幀檢測一次
- DETECT: 執行實際檢測
- CACHED: 使用緩存結果
- 可通過 --detection-interval 參數調整

💡 編輯提示:
- 編輯模式下選中的點會以黃色圓圈標示
- 控制點顯示編號便於識別
- 區域至少需要3個點，無法刪除到少於3個點
- 所有編輯操作會自動保存
        """
        logger.info(help_text)

    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            # 優化RTSP流設置以減少延遲
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小緩衝區
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 3000)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)
            # 設置為實時流模式
            self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
            self.cap.set(cv2.CAP_PROP_FPS, 25)  # 限制FPS以減少延遲
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
            
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 保存原始設定
            self.original_fps = fps
            self.original_width = width
            self.original_height = height
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            if self.save_video:
                # 獲取優化的視頻設定
                fourcc_str, opt_fps, opt_width, opt_height = self._get_optimized_video_settings(fps, width, height)
                fourcc = cv2.VideoWriter_fourcc(*fourcc_str)
                
                # 創建優化的視頻寫入器
                self.out = cv2.VideoWriter(self.output_path, fourcc, opt_fps, (opt_width, opt_height))
                
                # 保存優化後的設定供後續使用
                self.output_fps = opt_fps
                self.output_width = opt_width
                self.output_height = opt_height
                
                if not self.out.isOpened():
                    logger.error(f"無法創建視頻寫入器: {self.output_path}")
                    self.save_video = False
                else:
                    logger.info(f"✅ 優化視頻將保存到: {self.output_path} ({fourcc_str}格式)")
                    logger.info(f"   輸出設定: {opt_width}x{opt_height} @ {opt_fps}fps")
            
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """GPU优化的流处理"""
        if not self.connect_stream():
            return

        if display:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)

        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                frame_start = time.time()
                
                # 清理緩衝區以減少延遲（每10幀清理一次）
                if frame_count % 10 == 0:
                    # 快速讀取並丟棄積累的幀
                    for _ in range(2):
                        ret_temp, _ = self.cap.read()
                        if not ret_temp:
                            break
                
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    # 重新連接時清理緩衝區
                    if self.cap.isOpened():
                        self.cap.release()
                    if not self.connect_stream():
                        time.sleep(1)
                        continue
                    else:
                        continue
                
                frame_count += 1

                # 檢測間隔控制 - 修復版本
                should_detect = (frame_count % self.detection_interval == 1)

                if should_detect or self.detection_interval == 1:
                    # 執行檢測（每幀檢測或到達檢測間隔）
                    detection_start = time.time()
                    
                    # 人頭檢測使用主模型
                    with torch.no_grad():
                        if self.detector.device == 'cuda':
                            torch.cuda.synchronize()
                        head_results = self.detector.model([frame])
                        if self.detector.device == 'cuda':
                            torch.cuda.synchronize()
                    
                    # 車輛檢測使用專用模型（只在有車輛ROI時）
                    vehicle_results = None
                    if self.bus_regions and len(self.bus_regions) > 0:
                        with torch.no_grad():
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                            vehicle_results = self.detector.vehicle_model([frame])
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                    
                    multi_region_result = self.detector._process_multi_region_frame(
                        frame, head_results, vehicle_results, self.roi_regions, self.bus_regions, frame_count)
                    detection_time = time.time() - detection_start

                    # 緩存檢測結果
                    self.last_detection_result = multi_region_result
                    self.detection_frame_count += 1
                else:
                    # 警告：使用緩存結果可能不準確
                    if self.last_detection_result is not None:
                        multi_region_result = self.last_detection_result.copy()
                        # 重新繪製當前幀
                        bus_results = multi_region_result.get('bus_results', [])
                        bus_count = multi_region_result.get('bus_count', 0)
                        multi_region_result['annotated_frame'] = self.detector._draw_multi_region_results(
                            frame, multi_region_result['region_results'], multi_region_result['total_count'], 
                            bus_results, bus_count)
                        
                        # 添加緩存警告標示
                        cv2.putText(multi_region_result['annotated_frame'], 
                                   "CACHED RESULT - MAY BE OUTDATED", 
                                   (10, frame.shape[0] - 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 165, 255), 2)
                    else:
                        # 如果沒有緩存結果，執行一次檢測
                        detection_start = time.time()
                        
                        # 人頭檢測使用主模型
                        with torch.no_grad():
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                            head_results = self.detector.model([frame])
                            if self.detector.device == 'cuda':
                                torch.cuda.synchronize()
                        
                        # 車輛檢測使用專用模型（只在有車輛ROI時）
                        vehicle_results = None
                        if self.bus_regions and len(self.bus_regions) > 0:
                            with torch.no_grad():
                                if self.detector.device == 'cuda':
                                    torch.cuda.synchronize()
                                vehicle_results = self.detector.vehicle_model([frame])
                                if self.detector.device == 'cuda':
                                    torch.cuda.synchronize()
                        
                        multi_region_result = self.detector._process_multi_region_frame(
                            frame, head_results, vehicle_results, self.roi_regions, self.bus_regions, frame_count)
                        detection_time = time.time() - detection_start
                        self.last_detection_result = multi_region_result
                        self.detection_frame_count += 1
                    detection_time = 0  # 非檢測幀的檢測時間為0

                total_count = multi_region_result['total_count']
                region_results = multi_region_result['region_results']
                bus_count = multi_region_result.get('bus_count', 0)
                bus_results = multi_region_result.get('bus_results', [])
                annotated_frame = multi_region_result['annotated_frame']

                # 更新統計數據（只在檢測幀更新）
                if should_detect:
                    self._update_statistics(region_results, total_count)
                    # 更新公車統計
                    if bus_results:
                        self._update_bus_statistics(bus_results, bus_count)
                
                frame_time = time.time() - frame_start
                self.frame_times.append(frame_time)
                
                if self.detector.device == 'cuda':
                    gpu_memory = torch.cuda.memory_allocated() / 1024**2
                    self.gpu_memory_usage.append(gpu_memory)
                
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                # 顯示FPS和檢測信息
                detection_status = "DETECT" if should_detect else "CACHED"
                fps_text = f'FPS: {fps:.1f} | Det: {detection_time*1000:.1f}ms | {detection_status}'
                cv2.putText(annotated_frame, fps_text, (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 顯示檢測間隔信息
                interval_text = f'Detection Interval: {self.detection_interval} frames'
                cv2.putText(annotated_frame, interval_text, (10, 180),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
                
                # 繪製多個ROI區域
                self._draw_roi_regions(annotated_frame)
                
                # 顯示當前狀態信息
                self._draw_status_info(annotated_frame)

                if display:
                    if annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                        cv2.imshow(self.window_name, annotated_frame)
                    else:
                        logger.warning("annotated_frame 無效，未顯示")
                    
                    key = cv2.waitKey(1) & 0xFF
                    
                    # 調試：顯示按鍵信息
                    if key != 255:  # 255 表示沒有按鍵
                        logger.info(f"🔍 檢測到按鍵: '{chr(key) if 32 <= key <= 126 else f'Code:{key}'}' (ASCII: {key})")
                    
                    if key == ord('q'):
                        break
                    elif key == ord('c'):
                        self._clear_all_roi()
                    elif key == ord('n'):
                        self._start_new_region('person')
                    elif key == ord('b'):
                        self._start_new_region('bus')
                    elif key == ord('d'):
                        if self.edit_mode:
                            self._delete_selected_point()
                        else:
                            self._delete_selected_region()
                    elif key == ord('e'):
                        if self.selected_region_index >= 0:
                            if self.edit_mode:
                                self._exit_edit_mode()
                            else:
                                self.edit_mode = True
                                self.edit_point_index = -1
                                self.drag_mode = False
                                logger.info(f"✅ 進入編輯模式 - {self.selected_region_type}區域 {self.selected_region_index + 1}")
                                logger.info("編輯模式操作說明:")
                                logger.info("  - 左鍵點擊點附近: 選擇並拖拽點")
                                logger.info("  - 左鍵點擊邊線: 在該位置插入新點")
                                logger.info("  - 按 'd': 刪除選中的點")
                                logger.info("  - 右鍵或ESC: 退出編輯模式")
                        else:
                            logger.warning("⚠️ 請先選擇一個區域（中鍵點擊區域內部）")
                    elif key == 27:  # ESC鍵
                        if self.edit_mode:
                            self._exit_edit_mode()
                        else:
                            logger.info("按 'q' 退出程序")
                    elif key == ord('s'):
                        save_path = f"gpu_head_frame_{frame_count}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                    elif key == ord('i'):
                        self._print_region_info()
                    elif key == ord('h'):
                        self._print_help()
                    elif key >= ord('1') and key <= ord('9'):
                        # 數字鍵快速選擇區域
                        region_num = key - ord('1')  # 轉換為0-based索引
                        self._select_region_by_number(region_num)
                
                if self.save_video and self.out and annotated_frame is not None and isinstance(annotated_frame, np.ndarray):
                    # 如果輸出解析度與原始解析度不同，需要調整大小
                    if hasattr(self, 'output_width') and hasattr(self, 'output_height'):
                        if (annotated_frame.shape[1] != self.output_width or 
                            annotated_frame.shape[0] != self.output_height):
                            # 調整幀大小以匹配輸出設定
                            resized_frame = cv2.resize(annotated_frame, (self.output_width, self.output_height))
                            self.out.write(resized_frame)
                        else:
                            self.out.write(annotated_frame)
                    else:
                        self.out.write(annotated_frame)
                
                if frame_count % 100 == 0:
                    avg_fps = frame_count / elapsed_time
                    avg_gpu_memory = np.mean(self.gpu_memory_usage[-100:]) if self.gpu_memory_usage else 0
                    region_summary = ", ".join([f"{r['region_name']}: {r['count']}" for r in region_results])
                    detection_efficiency = (self.detection_frame_count / frame_count) * 100 if frame_count > 0 else 0
                    logger.info(f"帧 {frame_count}: 總人數 {total_count} ({region_summary}), "
                              f"平均FPS {avg_fps:.1f}, "
                              f"檢測效率 {detection_efficiency:.1f}% ({self.detection_frame_count}/{frame_count}), "
                              f"GPU内存 {avg_gpu_memory:.1f}MB")
                
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self._print_performance_summary()
            self.cleanup()
    
    def _print_performance_summary(self):
        """打印性能和統計總結"""
        if self.frame_times:
            avg_frame_time = np.mean(self.frame_times)
            avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0

            logger.info("=== GPU性能總結 ===")
            logger.info(f"平均幀處理時間: {avg_frame_time*1000:.1f}ms")
            logger.info(f"平均FPS: {avg_fps:.1f}")
            logger.info(f"檢測間隔設置: 每 {self.detection_interval} 幀檢測一次")
            logger.info(f"實際檢測幀數: {self.detection_frame_count}")

            if self.gpu_memory_usage:
                avg_gpu_memory = np.mean(self.gpu_memory_usage)
                max_gpu_memory = max(self.gpu_memory_usage)
                logger.info(f"平均GPU內存使用: {avg_gpu_memory:.1f}MB")
                logger.info(f"峰值GPU內存使用: {max_gpu_memory:.1f}MB")

        # 多區域統計總結
        logger.info("=== 多區域檢測統計總結 ===")
        logger.info(f"總ROI區域數: {len(self.roi_regions)}")

        stats = self._get_statistics_summary()
        logger.info(f"最終總人數: {stats['current_total']}")

        for region_name, region_stats in stats['region_stats'].items():
            logger.info(f"區域 {region_name}:")
            logger.info(f"  - 平均人數: {region_stats['avg_count']:.1f}")
            logger.info(f"  - 最大人數: {region_stats['max_count']}")
            logger.info(f"  - 總檢測次數: {region_stats['detection_count']}")
            logger.info(f"  - 累計檢測人數: {region_stats['total_detections']}")

        # 保存統計數據到文件
        self._save_statistics_report()

    def _save_statistics_report(self):
        """保存統計報告到文件"""
        try:
            report_file = f"detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            report_data = {
                'session_info': {
                    'start_time': datetime.now().isoformat(),
                    'rtsp_url': self.rtsp_url,
                    'total_regions': len(self.roi_regions),
                    'total_bus_regions': len(self.bus_regions),
                    'device': self.detector.device,
                    'model_type': self.detector.model_type
                },
                'roi_regions': self.roi_regions,
                'bus_regions': self.bus_regions,
                'statistics': self.total_statistics,
                'region_statistics': self.region_statistics,
                'bus_statistics': self.bus_statistics,
                'performance': {
                    'avg_fps': 1.0 / np.mean(self.frame_times) if self.frame_times else 0,
                    'avg_frame_time_ms': np.mean(self.frame_times) * 1000 if self.frame_times else 0,
                    'avg_gpu_memory_mb': np.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0,
                    'max_gpu_memory_mb': max(self.gpu_memory_usage) if self.gpu_memory_usage else 0
                }
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

            logger.info(f"統計報告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"保存統計報告失敗: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        cv2.waitKey(1)
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 清理模型引用
        if hasattr(self.detector, 'model'):
            del self.detector.model
        if hasattr(self.detector, 'vehicle_model'):
            del self.detector.vehicle_model
            
        logger.info("资源清理完成（包含双模型）")

def main():
    parser = argparse.ArgumentParser(description='GPU优化的YOLOv5头部检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1280x800',
                       help='RTSP流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型类型')
    parser.add_argument('--conf-threshold', type=float, default=0.15,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='批处理大小（GPU优化）')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='gpu_head_detection.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    parser.add_argument('--detection-interval', type=int, default=50,
                       help='檢測間隔（幀數），例如5表示每5幀檢測一次，預設為10（每10幀檢測一次）')
    parser.add_argument('--video-quality', type=str, default='medium',
                       choices=['high', 'medium', 'low', 'very_low'],
                       help='視頻品質設定 (high=高品質大檔案, medium=平衡, low=小檔案, very_low=最小檔案)')
    parser.add_argument('--target-fps', type=int, default=None,
                       help='目標輸出FPS (覆蓋品質設定的FPS)')
    parser.add_argument('--target-resolution', type=str, default=None,
                       help='目標解析度，格式: WIDTHxHEIGHT (例如: 1280x720)')

    args = parser.parse_args()
    
    # 解析目標解析度
    target_resolution = None
    if args.target_resolution:
        try:
            width, height = map(int, args.target_resolution.split('x'))
            target_resolution = (width, height)
            logger.info(f"設定目標解析度: {width}x{height}")
        except ValueError:
            logger.error(f"無效的解析度格式: {args.target_resolution}，應為 WIDTHxHEIGHT")
            return 1

    # 驗證檢測間隔參數
    if args.detection_interval < 1:
        logger.warning("檢測間隔不能小於1，已重設為1")
        args.detection_interval = 1
    elif args.detection_interval > 30:
        logger.warning("檢測間隔過大，已限制為30幀")
        args.detection_interval = 30
    
    if args.device == 'cuda':
        if not torch.cuda.is_available():
            logger.error("CUDA不可用！请检查GPU驱动和CUDA安装")
            return 1
    
    try:
        detector = GPUOptimizedHeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device,
            batch_size=args.batch_size
        )
        
        processor = GPUOptimizedRTSPProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path,
            detection_interval=args.detection_interval,
            video_quality=args.video_quality,
            target_fps=args.target_fps,
            target_resolution=target_resolution
        )
        
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
