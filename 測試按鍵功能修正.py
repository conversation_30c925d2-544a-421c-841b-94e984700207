#!/usr/bin/env python3
"""
測試按鍵功能修正
"""

import logging

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_detection_data_parsing():
    """測試檢測數據解析"""
    logger.info("=== 測試檢測數據解析 ===")
    
    # 模擬不同格式的檢測數據
    test_detections = [
        # 正常格式
        [100, 200, 300, 400, 0.85],
        # 字符串格式（可能導致錯誤）
        ['100', '200', '300', '400', '0.85'],
        # 浮點數格式
        [100.5, 200.7, 300.2, 400.8, 0.92],
        # 不完整數據
        [100, 200, 300],
        # 無效數據
        ['invalid', 'data', 'format', 'test'],
    ]
    
    def safe_parse_detection(detection):
        """安全解析檢測數據"""
        try:
            if isinstance(detection, (list, tuple)) and len(detection) >= 4:
                # 確保坐標是數字類型
                x1 = float(detection[0])
                y1 = float(detection[1])
                x2 = float(detection[2])
                y2 = float(detection[3])
                
                # 計算中心點
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                
                return True, (center_x, center_y)
            else:
                return False, None
                
        except (ValueError, TypeError, IndexError) as e:
            return False, str(e)
    
    # 測試每個檢測數據
    for i, detection in enumerate(test_detections):
        logger.info(f"測試檢測數據 {i + 1}: {detection}")
        success, result = safe_parse_detection(detection)
        
        if success:
            logger.info(f"  ✅ 解析成功: 中心點 {result}")
        else:
            logger.warning(f"  ❌ 解析失敗: {result}")
        logger.info("")

def test_key_mapping():
    """測試按鍵映射"""
    logger.info("=== 測試按鍵映射 ===")
    
    # 定義按鍵功能映射
    key_functions = {
        'q': '退出程序',
        'v': '切換WebSocket圖片/檢測統計顯示',
        'd': '刪除選中的區域',
        'n': '添加新的人頭檢測區域',
        'b': '添加新的公交車檢測區域',
        'h': '顯示幫助信息',
        's': '保存所有ROI區域到文件',
        'c': '清除所有ROI區域',
        'r': '清除當前正在繪製的區域',
        'e': '切換編輯模式',
        't': '切換區域類型 (人頭/公車)',
        '1-9': '選擇區域進行編輯'
    }
    
    logger.info("WebSocket處理器按鍵功能:")
    for key, function in key_functions.items():
        logger.info(f"  {key:4} - {function}")
    
    logger.info("")
    logger.info("重要修正:")
    logger.info("  ✅ 'd' 鍵: 從 '切換顯示' 改為 '刪除選中區域'")
    logger.info("  ✅ 'c' 鍵: 從 '清除當前區域' 改為 '清除所有區域'")
    logger.info("  ✅ 'r' 鍵: 從 '重置所有區域' 改為 '清除當前區域'")

def test_region_data_structure():
    """測試區域數據結構"""
    logger.info("=== 測試區域數據結構 ===")
    
    # 模擬區域數據
    test_region = {
        'id': 0,
        'name': 'Person_Region_1',
        'type': 'person',
        'points': [[100, 100], [200, 100], [200, 200], [100, 200]],
        'closed': True
    }
    
    # 測試安全訪問
    def safe_get_region_info(region):
        """安全獲取區域信息"""
        info = {
            'id': region.get('id', -1),
            'name': region.get('name', 'Unknown'),
            'type': region.get('type', 'unknown'),
            'points': region.get('points', []),
            'closed': region.get('closed', True),
            'point_count': len(region.get('points', []))
        }
        return info
    
    logger.info("測試區域數據:")
    info = safe_get_region_info(test_region)
    for key, value in info.items():
        logger.info(f"  {key}: {value}")
    
    # 測試缺少屬性的區域
    incomplete_region = {
        'name': 'Incomplete_Region',
        'points': [[50, 50], [150, 50], [150, 150]]
        # 缺少其他屬性
    }
    
    logger.info("")
    logger.info("測試不完整區域數據:")
    info = safe_get_region_info(incomplete_region)
    for key, value in info.items():
        logger.info(f"  {key}: {value}")

def test_statistics_calculation():
    """測試統計計算"""
    logger.info("=== 測試統計計算 ===")
    
    # 模擬檢測結果
    detection_results = {
        'total_count': 5,
        'bus_count': 2,
        'region_results': [
            {'region_id': 0, 'region_name': 'Person_Region_1', 'count': 3},
            {'region_id': 1, 'region_name': 'Person_Region_2', 'count': 2}
        ],
        'bus_results': [
            {'region_id': 0, 'region_name': 'Bus_Region_1', 'count': 2}
        ]
    }
    
    # 測試統計計算
    def calculate_stats(results):
        """計算統計信息"""
        if isinstance(results, dict):
            total_count = results.get('total_count', 0)
            bus_count = results.get('bus_count', 0)
            region_results = results.get('region_results', [])
            
            roi_count = sum(result.get('count', 0) for result in region_results)
            
            return {
                'total_count': total_count,
                'roi_count': roi_count,
                'bus_count': bus_count,
                'region_count': len(region_results)
            }
        else:
            return {'error': 'Invalid data format'}
    
    stats = calculate_stats(detection_results)
    logger.info("統計結果:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")

def main():
    """主測試函數"""
    logger.info("開始按鍵功能修正測試...")
    
    test_detection_data_parsing()
    test_key_mapping()
    test_region_data_structure()
    test_statistics_calculation()
    
    logger.info("=== 測試完成 ===")
    logger.info("修正總結:")
    logger.info("1. ✅ 修正按鍵功能映射")
    logger.info("2. ✅ 改進檢測數據解析")
    logger.info("3. ✅ 修正統計計算邏輯")
    logger.info("4. ✅ 增強錯誤處理")

if __name__ == "__main__":
    main()
