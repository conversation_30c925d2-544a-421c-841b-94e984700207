# 鍵盤快捷鍵功能修復指南

## 問題描述

在 `gpu_optimized_head_detection.py` 中，以下鍵盤快捷鍵功能無效：
- **'n'** - 添加ROI區域功能
- **'b'** - 添加公交車區域功能  
- **'v'** - 顯示切換功能

## 問題原因

1. **缺少方法實現**: 主文件中缺少 `_toggle_display`、`_add_new_roi_region`、`_add_new_bus_region` 方法
2. **缺少屬性初始化**: `is_drawing_roi` 屬性未在 `__init__` 方法中初始化
3. **鍵盤事件處理不完整**: 鍵盤事件處理代碼中缺少對 'n'、'b'、'v' 鍵的處理

## 修復內容

### 1. 添加缺少的方法

#### `_toggle_display()` 方法
```python
def _toggle_display(self):
    """切換顯示開關"""
    current_time = time.time()
    if current_time - self.last_display_toggle_time > 0.5:  # 防止快速切換
        self.display_enabled = not self.display_enabled
        self.last_display_toggle_time = current_time
        status = "開啟" if self.display_enabled else "關閉"
        logger.info(f"顯示已{status}")
        
        if not self.display_enabled and self.window_created:
            cv2.destroyWindow(self.window_name)
            self.window_created = False
        elif self.display_enabled and not self.window_created:
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.setMouseCallback(self.window_name, self._mouse_callback)
            self.window_created = True
```

#### `_add_new_roi_region()` 方法
```python
def _add_new_roi_region(self):
    """添加新的ROI區域"""
    logger.info("開始添加新的ROI區域 - 使用鼠標左鍵添加點，右鍵完成")
    self.current_region_points = []
    self.is_drawing_roi = True
    self.current_region_type = "person"
    self.current_region_closed = False
```

#### `_add_new_bus_region()` 方法
```python
def _add_new_bus_region(self):
    """添加新的公交車區域"""
    logger.info("開始添加新的公交車區域 - 使用鼠標左鍵添加點，右鍵完成")
    self.current_region_points = []
    self.is_drawing_roi = True
    self.current_region_type = "bus"
    self.current_region_closed = False
```

### 2. 添加缺少的屬性初始化

在 `__init__` 方法中添加：
```python
self.is_drawing_roi = False  # ROI繪製狀態
```

### 3. 更新鍵盤事件處理

```python
# 處理按鍵
if key == ord('q'):
    logger.info("用戶按下 'q'，退出程序")
    break
elif key == ord('v'):           # 新增: v鍵切換顯示
    self._toggle_display()
elif key == ord('d'):           # 保留: d鍵切換顯示
    self._toggle_display()
elif key == ord('n'):           # 新增: n鍵添加ROI區域
    self._add_new_roi_region()
elif key == ord('b'):           # 新增: b鍵添加公交車區域
    self._add_new_bus_region()
elif key == ord('h'):
    self._show_help()
# ... 其他按鍵處理
```

### 4. 更新幫助信息

更新 `_show_help()` 方法中的幫助文本，包含新的快捷鍵說明：

```
基本操作:
  q     - 退出程序
  v     - 切換顯示模式 (顯示/隱藏視頻窗口)  # 新增
  d     - 切換顯示模式 (顯示/隱藏視頻窗口)
  h     - 顯示此幫助信息

ROI區域操作:
  n     - 添加新的ROI區域                    # 新增
  b     - 添加新的公交車區域                # 新增
  左鍵  - 添加ROI區域頂點
  右鍵  - 完成當前ROI區域繪製
  s     - 保存所有ROI區域到文件
  c     - 清除當前正在繪製的區域
  r     - 重置所有ROI區域
  t     - 切換區域類型 (人頭/公車)
```

## 功能說明

### 'v' 鍵 - 顯示切換功能
- **功能**: 切換視頻窗口的顯示/隱藏
- **防抖**: 0.5秒內防止重複切換
- **窗口管理**: 自動創建/銷毀窗口和鼠標回調

### 'n' 鍵 - 添加ROI區域功能
- **功能**: 開始添加新的人頭檢測ROI區域
- **操作流程**: 
  1. 按 'n' 鍵進入ROI繪製模式
  2. 使用鼠標左鍵添加多邊形頂點
  3. 使用鼠標右鍵完成區域繪製
- **區域類型**: 自動設置為 "person"

### 'b' 鍵 - 添加公交車區域功能
- **功能**: 開始添加新的公交車檢測區域
- **操作流程**: 與ROI區域相同
- **區域類型**: 自動設置為 "bus"

## 驗證修復

### 測試腳本
運行測試腳本確認修復成功：
```bash
python tmp_rovodev_test_keyboard_shortcuts.py
```

### 手動測試
1. 運行主程序：
   ```bash
   python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
   ```

2. 測試快捷鍵：
   - 按 'v' 鍵測試顯示切換
   - 按 'n' 鍵測試ROI區域添加
   - 按 'b' 鍵測試公交車區域添加
   - 按 'h' 鍵查看更新的幫助信息

### 預期結果
- **'v' 鍵**: 視頻窗口應該顯示/隱藏切換，控制台顯示狀態信息
- **'n' 鍵**: 控制台顯示 "開始添加新的ROI區域" 信息，進入繪製模式
- **'b' 鍵**: 控制台顯示 "開始添加新的公交車區域" 信息，進入繪製模式

## 相關屬性和狀態

### 新增/修復的屬性
- `is_drawing_roi`: ROI繪製狀態標誌
- `display_enabled`: 顯示開關狀態
- `last_display_toggle_time`: 上次切換顯示的時間（防抖）

### ROI繪製狀態管理
- `current_region_points`: 當前區域的點列表
- `current_region_type`: 當前區域類型 ("person" 或 "bus")
- `current_region_closed`: 當前區域是否已完成

## 技術細節

### 防抖機制
顯示切換功能實現了0.5秒的防抖機制，防止用戶快速按鍵導致的頻繁切換。

### 窗口管理
- 隱藏顯示時自動銷毀窗口
- 重新顯示時自動創建窗口並設置鼠標回調

### 狀態同步
所有ROI相關的狀態變量都會在添加新區域時正確初始化和同步。

## 相關文件

- `gpu_optimized_head_detection.py` - 主程序（已修復）
- `tmp_rovodev_test_keyboard_shortcuts.py` - 測試腳本
- `KEYBOARD_SHORTCUTS_FIX.md` - 本修復文檔

---

**狀態**: ✅ 已修復  
**測試**: ✅ 需要驗證  
**功能**: 🎯 'n'、'b'、'v' 鍵功能已實現