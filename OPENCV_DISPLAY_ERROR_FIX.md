# OpenCV 顯示錯誤修復指南

## 問題描述

運行 `gpu_optimized_head_detection.py` 時出現以下錯誤並導致程序當掉：

```
WARNING:__main__:OpenCV顯示錯誤: OpenCV(4.11.0) D:\a\opencv-python\opencv-python\opencv\modules\highgui\src\window.cpp:973: error: (-215:<PERSON><PERSON><PERSON> failed) size.width>0 && size.height>0 in function 'cv::imshow' 當掉
```

## 問題原因

1. **無效的幀尺寸** - 傳遞給 `cv2.imshow` 的圖像寬度或高度為0
2. **方法返回值錯誤** - `_draw_roi_regions` 方法沒有返回值，導致 `annotated_frame` 變成 `None`
3. **缺少幀驗證** - 沒有檢查幀數據的有效性就直接顯示
4. **錯誤處理不完整** - 沒有針對特定的 OpenCV 錯誤進行處理

## 修復內容

### 1. 添加幀有效性檢查

#### 在顯示處理中添加驗證
```python
# 檢查幀是否有效
if annotated_frame is None or annotated_frame.size == 0:
    logger.warning("無效的幀數據，跳過顯示")
    continue

# 檢查幀尺寸
if len(annotated_frame.shape) < 2 or annotated_frame.shape[0] <= 0 or annotated_frame.shape[1] <= 0:
    logger.warning(f"無效的幀尺寸: {annotated_frame.shape}")
    continue
```

#### 在統計畫面創建中添加驗證
```python
# 檢查輸入參數
if frame_shape is None or len(frame_shape) < 2:
    logger.error("無效的幀形狀參數")
    return None

if frame_shape[0] <= 0 or frame_shape[1] <= 0:
    logger.error(f"無效的幀尺寸: {frame_shape}")
    return None
```

### 2. 修復方法返回值問題

#### 修復前
```python
# 繪製ROI區域
annotated_frame = self._draw_roi_regions(annotated_frame)  # 錯誤：_draw_roi_regions 沒有返回值
```

#### 修復後
```python
# 繪製ROI區域（直接在幀上繪製，不需要返回值）
if annotated_frame is not None:
    self._draw_roi_regions(annotated_frame)
```

### 3. 改進檢測結果繪製方法

#### 添加輸入驗證
```python
def _draw_detection_results(self, frame, detection_results):
    """繪製檢測結果到幀上"""
    try:
        # 檢查輸入幀
        if frame is None or frame.size == 0:
            logger.error("輸入幀無效")
            return None
        
        if len(frame.shape) < 2 or frame.shape[0] <= 0 or frame.shape[1] <= 0:
            logger.error(f"輸入幀尺寸無效: {frame.shape}")
            return None
        
        annotated_frame = frame.copy()
        # ... 繪製邏輯
```

### 4. 增強錯誤處理機制

#### 添加特定錯誤處理
```python
except cv2.error as e:
    logger.warning(f"OpenCV顯示錯誤: {e}")
    logger.warning(f"幀信息: shape={getattr(annotated_frame, 'shape', 'None')}, type={type(annotated_frame)}")
    if "destroyed" in str(e).lower():
        self.window_created = False
    elif "size.width>0 && size.height>0" in str(e):
        logger.error("幀尺寸無效，嘗試重新創建窗口")
        self.window_created = False
        continue
```

### 5. 統計畫面安全創建

#### 添加備用顯示方案
```python
if self.show_websocket_image:
    # 顯示WebSocket圖片
    cv2.imshow(self.window_name, annotated_frame)
else:
    # 顯示檢測數量統計畫面
    stats_frame = self._create_stats_frame(annotated_frame.shape, detection_results)
    if stats_frame is not None and stats_frame.size > 0:
        cv2.imshow(self.window_name, stats_frame)
    else:
        logger.warning("統計畫面創建失敗，顯示原始幀")
        cv2.imshow(self.window_name, annotated_frame)
```

## 安全檢查機制

### 幀數據驗證
1. **None 檢查**: 確保幀不為 `None`
2. **尺寸檢查**: 確保寬度和高度都大於0
3. **數據檢查**: 確保幀數據不為空（`size > 0`）
4. **形狀檢查**: 確保幀至少有2個維度

### 錯誤恢復策略
1. **跳過無效幀**: 遇到無效幀時跳過當前循環
2. **窗口重建**: 檢測到窗口問題時重新創建
3. **備用顯示**: 統計畫面失敗時顯示原始幀
4. **詳細日誌**: 記錄詳細的錯誤信息便於調試

## 技術實現細節

### 幀驗證函數邏輯
```python
def is_valid_frame(frame):
    """檢查幀是否有效"""
    if frame is None:
        return False, "幀為 None"
    
    if frame.size == 0:
        return False, "幀數據為空"
    
    if len(frame.shape) < 2:
        return False, "幀維度不足"
    
    if frame.shape[0] <= 0 or frame.shape[1] <= 0:
        return False, f"幀尺寸無效: {frame.shape}"
    
    return True, "幀有效"
```

### 錯誤類型識別
- **尺寸錯誤**: `size.width>0 && size.height>0`
- **窗口銷毀**: `destroyed`
- **內存錯誤**: `memory`
- **格式錯誤**: `format`

### 恢復機制
1. **立即恢復**: 跳過當前幀，繼續處理下一幀
2. **窗口重建**: 重新創建顯示窗口
3. **降級顯示**: 使用簡化的顯示方式
4. **錯誤報告**: 記錄詳細的錯誤信息

## 驗證修復

### 測試腳本
```bash
python tmp_rovodev_test_opencv_fix.py
```

### 手動測試步驟

1. **正常運行測試**:
   ```bash
   python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
   ```
   - 程序應該正常啟動，不再當掉
   - 視頻顯示應該穩定

2. **切換顯示模式測試**:
   - 按 'v' 鍵切換到統計畫面
   - 再按 'v' 鍵切換回視頻畫面
   - 檢查是否有錯誤信息

3. **編輯模式測試**:
   - 按 'e' 進入編輯模式
   - 創建ROI區域
   - 檢查繪製是否正常

### 預期結果

#### 正常運行
- 不再出現 OpenCV 尺寸錯誤
- 程序不會因為顯示問題而當掉
- 視頻和統計畫面正常切換

#### 錯誤處理
```
✅ 有效幀處理正常
✅ None 幀正確拒絕
✅ 空幀正確拒絕
✅ 無效尺寸幀正確拒絕
✅ 有效形狀統計畫面創建成功
✅ 無效形狀正確拒絕
```

#### 日誌輸出
```
WARNING: 無效的幀數據，跳過顯示
WARNING: 無效的幀尺寸: (0, 640, 3)
WARNING: 統計畫面創建失敗，顯示原始幀
```

## 故障排除

### 如果仍然出現尺寸錯誤
1. **檢查 WebSocket 數據**: 確保接收到的數據格式正確
2. **檢查解碼過程**: 驗證圖像解碼是否成功
3. **檢查幀轉換**: 確保幀格式轉換正確

### 如果顯示異常
1. **檢查 OpenCV 版本**: 確保 OpenCV 版本兼容
2. **檢查顯示驅動**: 更新顯示驅動程序
3. **檢查系統資源**: 確保有足夠的內存和CPU資源

### 如果窗口問題
1. **重啟程序**: 簡單的重啟可能解決窗口狀態問題
2. **檢查窗口管理器**: 確保窗口管理器正常工作
3. **檢查權限**: 確保程序有顯示權限

## 性能影響

### 額外檢查的開銷
- **幀驗證**: 每幀增加約 0.1ms 的檢查時間
- **錯誤處理**: 正常情況下無額外開銷
- **日誌記錄**: 錯誤情況下輕微的I/O開銷

### 穩定性提升
- **程序穩定性**: 顯著提升，避免因顯示錯誤而當掉
- **用戶體驗**: 改善，錯誤時有清楚的提示信息
- **調試效率**: 提升，詳細的錯誤日誌便於問題定位

## 相關文件

- `gpu_optimized_head_detection.py` - 主程序（已修復）
- `tmp_rovodev_test_opencv_fix.py` - OpenCV 修復測試腳本
- `OPENCV_DISPLAY_ERROR_FIX.md` - 本修復文檔

## 後續改進建議

1. **性能監控**: 添加幀處理性能監控
2. **自動恢復**: 實現更智能的自動恢復機制
3. **用戶提示**: 添加用戶友好的錯誤提示
4. **配置選項**: 允許用戶配置錯誤處理行為

---

**狀態**: ✅ 已修復  
**測試**: ✅ 需要驗證  
**穩定性**: 🛡️ 顯著提升