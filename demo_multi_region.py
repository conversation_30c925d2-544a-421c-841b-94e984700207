#!/usr/bin/env python3
"""
多區域頭部檢測系統演示腳本
使用攝像頭或視頻文件進行演示
"""

import cv2
import numpy as np
import argparse
import logging
from gpu_optimized_head_detection import GPUOptimizedHeadDetector, GPUOptimizedRTSPProcessor

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiRegionDemo:
    def __init__(self, source=0, model_type='person', conf_threshold=0.4):
        """
        多區域演示類
        
        Args:
            source: 視頻源（0為攝像頭，或視頻文件路徑）
            model_type: 模型類型
            conf_threshold: 置信度閾值
        """
        self.source = source
        self.detector = GPUOptimizedHeadDetector(
            model_type=model_type,
            conf_threshold=conf_threshold,
            device='cuda'
        )
        
    def run_demo(self):
        """運行演示"""
        logger.info("開始多區域頭部檢測演示")
        logger.info("使用攝像頭或視頻文件作為輸入源")
        
        # 如果source是數字，使用攝像頭
        if isinstance(self.source, int) or self.source.isdigit():
            cap = cv2.VideoCapture(int(self.source))
            logger.info(f"使用攝像頭 {self.source}")
        else:
            cap = cv2.VideoCapture(self.source)
            logger.info(f"使用視頻文件: {self.source}")
        
        if not cap.isOpened():
            logger.error("無法打開視頻源")
            return
        
        # 創建處理器（模擬RTSP處理器的功能）
        processor = DemoProcessor(self.detector)
        
        try:
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    logger.warning("無法讀取幀")
                    break
                
                frame_count += 1
                
                # 處理幀
                annotated_frame = processor.process_frame(frame, frame_count)
                
                # 顯示結果
                cv2.imshow('Multi-Region Head Detection Demo', annotated_frame)
                
                # 處理鍵盤輸入
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('c'):
                    processor.clear_all_regions()
                elif key == ord('n'):
                    processor.start_new_region()
                elif key == ord('d'):
                    processor.delete_selected_region()
                elif key == ord('i'):
                    processor.print_region_info()
                elif key == ord('h'):
                    processor.print_help()
                
        except KeyboardInterrupt:
            logger.info("用戶中斷演示")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            processor.cleanup()

class DemoProcessor:
    """演示處理器，簡化版的多區域處理"""
    
    def __init__(self, detector):
        self.detector = detector
        self.roi_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        self.region_statistics = {}
        
        # 設置鼠標回調
        cv2.namedWindow('Multi-Region Head Detection Demo', cv2.WINDOW_NORMAL)
        cv2.setMouseCallback('Multi-Region Head Detection Demo', self._mouse_callback)
        
        logger.info("演示處理器已初始化")
        logger.info("按 'h' 查看控制說明")
    
    def _mouse_callback(self, event, x, y, flags, param):
        """鼠標回調函數"""
        if event == cv2.EVENT_LBUTTONDOWN:
            if self.current_region_closed:
                logger.warning("當前區域已關閉，請按 'n' 開始新區域")
                return
            self.current_region_points.append([x, y])
            logger.info(f"添加點: ({x}, {y}) - 總點數: {len(self.current_region_points)}")
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            if not self.current_region_closed and len(self.current_region_points) > 2:
                self._add_new_region()
                logger.info("區域已完成並保存")
    
    def _add_new_region(self):
        """添加新區域"""
        if len(self.current_region_points) > 2:
            region_id = len(self.roi_regions)
            region_name = f"Region_{region_id + 1}"
            new_region = {
                'id': region_id,
                'name': region_name,
                'points': self.current_region_points.copy(),
                'closed': True
            }
            self.roi_regions.append(new_region)
            self.current_region_points = []
            self.current_region_closed = False
            return region_id
        return -1
    
    def process_frame(self, frame, frame_count):
        """處理單幀"""
        # 使用多區域檢測
        results = self.detector.detect_heads_batch([frame], roi_regions=self.roi_regions)
        multi_region_result = results[0]
        
        annotated_frame = multi_region_result['annotated_frame']
        
        # 繪製ROI區域
        self._draw_roi_regions(annotated_frame)
        
        # 添加幀計數
        cv2.putText(annotated_frame, f'Frame: {frame_count}', 
                   (annotated_frame.shape[1] - 150, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return annotated_frame
    
    def _draw_roi_regions(self, frame):
        """繪製ROI區域"""
        colors = [(0, 255, 255), (0, 255, 0), (255, 0, 0), (255, 0, 255)]
        
        # 繪製完成的區域
        for i, region in enumerate(self.roi_regions):
            if region['closed'] and len(region['points']) > 2:
                color = colors[i % len(colors)]
                roi_np = np.array([region['points']], dtype=np.int32)
                thickness = 4 if i == self.selected_region_index else 2
                cv2.polylines(frame, roi_np, isClosed=True, color=color, thickness=thickness)
                
                # 區域標籤
                if len(region['points']) > 0:
                    label_pos = tuple(region['points'][0])
                    cv2.putText(frame, region['name'], 
                              (label_pos[0], label_pos[1] - 10),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 繪製當前正在繪製的區域
        if len(self.current_region_points) > 0:
            current_color = (0, 0, 255)
            
            if len(self.current_region_points) > 1:
                roi_np = np.array([self.current_region_points], dtype=np.int32)
                cv2.polylines(frame, roi_np, isClosed=False, color=current_color, thickness=2)
            
            for point in self.current_region_points:
                cv2.circle(frame, tuple(point), 5, current_color, -1)
            
            if len(self.current_region_points) > 0:
                text = f"Drawing: {len(self.current_region_points)} points"
                cv2.putText(frame, text, (10, frame.shape[0] - 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, current_color, 2)
    
    def clear_all_regions(self):
        """清除所有區域"""
        self.roi_regions = []
        self.current_region_points = []
        self.current_region_closed = False
        self.selected_region_index = -1
        logger.info("所有區域已清除")
    
    def start_new_region(self):
        """開始新區域"""
        if not self.current_region_closed and len(self.current_region_points) > 0:
            logger.warning("當前區域未完成，請先右鍵完成")
            return
        self.current_region_points = []
        self.current_region_closed = False
        logger.info("開始繪製新區域")
    
    def delete_selected_region(self):
        """刪除選中區域"""
        if self.selected_region_index >= 0 and self.selected_region_index < len(self.roi_regions):
            deleted_region = self.roi_regions.pop(self.selected_region_index)
            self.selected_region_index = -1
            logger.info(f"已刪除區域: {deleted_region['name']}")
        else:
            logger.warning("沒有選中的區域可刪除")
    
    def print_region_info(self):
        """打印區域信息"""
        logger.info("=== 區域信息 ===")
        logger.info(f"總區域數: {len(self.roi_regions)}")
        for i, region in enumerate(self.roi_regions):
            logger.info(f"區域 {i+1}: {region['name']} - 點數: {len(region['points'])}")
    
    def print_help(self):
        """打印幫助"""
        help_text = """
=== 演示控制說明 ===
鼠標: 左鍵添加點，右鍵完成區域
鍵盤: q=退出, c=清除, n=新區域, d=刪除, i=信息, h=幫助
        """
        logger.info(help_text)
    
    def cleanup(self):
        """清理"""
        logger.info("演示結束")

def main():
    parser = argparse.ArgumentParser(description='多區域頭部檢測演示')
    parser.add_argument('--source', type=str, default='0',
                       help='視頻源（0為攝像頭，或視頻文件路徑）')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], help='模型類型')
    parser.add_argument('--conf-threshold', type=float, default=0.4,
                       help='檢測置信度閾值')
    
    args = parser.parse_args()
    
    try:
        demo = MultiRegionDemo(
            source=args.source,
            model_type=args.model_type,
            conf_threshold=args.conf_threshold
        )
        demo.run_demo()
    except Exception as e:
        logger.error(f"演示運行出錯: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
