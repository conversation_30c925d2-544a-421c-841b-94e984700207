# YOLOv5人数检测系统依赖包
torch>=1.9.0
torchvision>=0.10.0
opencv-python>=4.5.0
numpy>=1.21.0
pandas>=1.3.0
Pillow>=8.3.0
PyYAML>=5.4.0
requests>=2.25.0
tqdm>=4.61.0
matplotlib>=3.3.0
seaborn>=0.11.0

# YOLOv5相关依赖
ultralytics>=8.0.0

# WebSocket支持 - 修復 WebSocketApp 錯誤
websocket-client>=1.0.0

# 可选：GPU支持（如果有NVIDIA GPU）
# torch>=1.9.0+cu111 -f https://download.pytorch.org/whl/torch_stable.html
# torchvision>=0.10.0+cu111 -f https://download.pytorch.org/whl/torch_stable.html