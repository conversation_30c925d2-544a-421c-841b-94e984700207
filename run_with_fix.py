#!/usr/bin/env python3
"""
運行修復後的 GPU 檢測程序
確保所有修復都正確應用
"""

import sys
import os

def main():
    print("🚀 啟動 PyTorch 2.7+ 修復版 GPU 檢測程序")
    print("=" * 60)
    
    # 1. 首先執行終極修復
    try:
        from tmp_rovodev_ultimate_fix import ultimate_pytorch_fix, test_model_loading
        print("📦 載入終極修復模組...")
        ultimate_pytorch_fix()
        
        # 測試模型加載
        print("\n🧪 預先測試模型加載...")
        if test_model_loading():
            print("✅ 模型加載測試通過！")
        else:
            print("⚠️ 模型加載測試失敗，但繼續運行...")
            
    except Exception as e:
        print(f"⚠️ 終極修復失敗: {e}")
        print("🔄 嘗試基本修復...")
        
        # 基本修復
        import torch
        import warnings
        
        os.environ.update({
            'TORCH_SERIALIZATION_SAFE_GLOBALS': '1',
            'PYTORCH_DISABLE_SAFE_GLOBALS': '1'
        })
        
        # 修補 torch.load
        if not hasattr(torch, '_emergency_patched'):
            original_load = torch.load
            def emergency_patched_load(*args, **kwargs):
                kwargs.setdefault('weights_only', False)
                return original_load(*args, **kwargs)
            torch.load = emergency_patched_load
            torch._emergency_patched = True
        
        warnings.filterwarnings("ignore")
        print("✅ 基本修復完成")
    
    print("\n" + "=" * 60)
    print("🎯 啟動主程序...")
    print("=" * 60)
    
    # 2. 導入並運行主程序
    try:
        import gpu_optimized_head_detection
        print("✅ 主程序模組載入成功")
        
        # 這裡可以添加主程序的啟動邏輯
        # 例如：gpu_optimized_head_detection.main()
        
    except Exception as e:
        print(f"❌ 主程序啟動失敗: {e}")
        print("\n💡 建議解決方案:")
        print("1. 檢查所有依賴是否正確安裝")
        print("2. 嘗試清理 torch hub 緩存")
        print("3. 考慮降級到 PyTorch 2.5.x")
        return 1
    
    print("\n🎉 程序啟動完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())