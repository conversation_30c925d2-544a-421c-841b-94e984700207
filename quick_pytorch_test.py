#!/usr/bin/env python3
"""
快速測試 PyTorch 2.7+ 修復是否有效
"""

import torch
import warnings

print(f"🔍 PyTorch 版本: {torch.__version__}")
print(f"🔍 CUDA 可用: {torch.cuda.is_available()}")

# 1. 先執行終極修復
try:
    from tmp_rovodev_ultimate_fix import ultimate_pytorch_fix
    ultimate_pytorch_fix()
    print("✅ 終極修復已應用")
except Exception as e:
    print(f"⚠️ 終極修復失敗: {e}")

# 2. 測試 torch.hub.load
print("\n🧪 測試 torch.hub.load...")
try:
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore")
        
        # 嘗試加載最小的 YOLOv5 模型
        model = torch.hub.load('ultralytics/yolov5', 'yolov5n', 
                              pretrained=True, 
                              force_reload=True,
                              trust_repo=True, 
                              verbose=False)
        
    print("✅ YOLOv5n 模型加載成功！")
    
    # 簡單推理測試
    dummy_input = torch.randn(1, 3, 640, 640)
    with torch.no_grad():
        results = model(dummy_input)
    print("✅ 模型推理測試成功！")
    
    # 清理
    del model, dummy_input, results
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    print("\n🎉 所有測試通過！現在可以運行主程序了。")
    
except Exception as e:
    print(f"❌ 測試失敗: {e}")
    print("\n🔧 嘗試清理緩存...")
    
    try:
        import shutil
        import os
        cache_dir = torch.hub.get_dir()
        ultralytics_cache = os.path.join(cache_dir, "ultralytics_yolov5_master")
        
        if os.path.exists(ultralytics_cache):
            shutil.rmtree(ultralytics_cache)
            print(f"✅ 已清理緩存: {ultralytics_cache}")
            
            # 重新測試
            print("🔄 重新測試...")
            model = torch.hub.load('ultralytics/yolov5', 'yolov5n', 
                                  pretrained=True, 
                                  force_reload=True,
                                  trust_repo=True, 
                                  verbose=False)
            print("✅ 清理緩存後模型加載成功！")
            del model
        else:
            print("⚠️ 未找到需要清理的緩存")
            
    except Exception as e2:
        print(f"❌ 清理緩存也失敗: {e2}")
        print("\n💡 建議:")
        print("1. 手動刪除緩存目錄: C:\\Users\\<USER>\\.cache\\torch\\hub")
        print("2. 或者降級 PyTorch: pip install torch==2.5.1 torchvision==0.20.1")