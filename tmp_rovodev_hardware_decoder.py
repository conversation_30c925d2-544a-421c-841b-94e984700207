#!/usr/bin/env python3
"""
硬體解碼器自動偵測模組
支援 CPU 與 GPU 硬解加速的自動偵測和配置
"""

import cv2
import subprocess
import logging
import time
import numpy as np

logger = logging.getLogger(__name__)

class HardwareVideoDecoder:
    """硬體視頻解碼器 - 自動偵測並啟用最佳硬解加速"""
    
    def __init__(self, rtsp_url, auto_detect=True):
        self.rtsp_url = rtsp_url
        self.cap = None
        self.decoder_type = 'software'  # 預設軟體解碼
        self.hardware_capabilities = self._detect_hardware_capabilities()
        
        if auto_detect:
            self.decoder_type = self._auto_select_decoder()
        
        logger.info(f"🎬 視頻解碼器初始化完成: {self.decoder_type}")
    
    def _detect_hardware_capabilities(self):
        """偵測硬體解碼能力"""
        capabilities = {
            'nvidia_cuvid': False,
            'intel_qsv': False,
            'amd_amf': False,
            'cpu_optimized': False,
            'ffmpeg_available': False,
            'opencv_version': cv2.__version__
        }
        
        logger.info("🔍 偵測硬體解碼能力...")
        
        # 檢查 FFmpeg 是否可用
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                capabilities['ffmpeg_available'] = True
                ffmpeg_output = result.stdout.lower()
                
                # 檢查 NVIDIA CUVID 支援
                if 'cuvid' in ffmpeg_output or 'nvdec' in ffmpeg_output:
                    capabilities['nvidia_cuvid'] = True
                    logger.info("✅ NVIDIA CUVID 硬體解碼可用")
                
                # 檢查 Intel Quick Sync 支援
                if 'qsv' in ffmpeg_output:
                    capabilities['intel_qsv'] = True
                    logger.info("✅ Intel Quick Sync 硬體解碼可用")
                
                # 檢查 AMD AMF 支援
                if 'amf' in ffmpeg_output:
                    capabilities['amd_amf'] = True
                    logger.info("✅ AMD AMF 硬體解碼可用")
                    
            else:
                logger.warning("⚠️ FFmpeg 不可用")
                
        except Exception as e:
            logger.warning(f"⚠️ 無法檢測 FFmpeg: {e}")
        
        # 檢查 OpenCV 硬體加速支援
        try:
            # 檢查 OpenCV 是否支援硬體加速
            backends = cv2.videoio_registry.getBackends()
            logger.info(f"OpenCV 支援的後端: {backends}")
            
            # 檢查是否有 CUDA 支援
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                capabilities['opencv_cuda'] = True
                logger.info("✅ OpenCV CUDA 支援可用")
            
        except Exception as e:
            logger.debug(f"OpenCV 硬體加速檢測: {e}")
        
        # CPU 優化總是可用
        capabilities['cpu_optimized'] = True
        logger.info("✅ CPU 優化解碼可用")
        
        return capabilities
    
    def _auto_select_decoder(self):
        """自動選擇最佳解碼器"""
        logger.info("🎯 自動選擇最佳硬體解碼器...")
        
        # 優先順序：NVIDIA > Intel > AMD > CPU優化 > 軟體解碼
        if self.hardware_capabilities['nvidia_cuvid']:
            if self._test_decoder('nvidia_cuvid'):
                logger.info("🚀 選擇 NVIDIA CUVID 硬體解碼")
                return 'nvidia_cuvid'
        
        if self.hardware_capabilities['intel_qsv']:
            if self._test_decoder('intel_qsv'):
                logger.info("🚀 選擇 Intel Quick Sync 硬體解碼")
                return 'intel_qsv'
        
        if self.hardware_capabilities['amd_amf']:
            if self._test_decoder('amd_amf'):
                logger.info("🚀 選擇 AMD AMF 硬體解碼")
                return 'amd_amf'
        
        if self.hardware_capabilities['cpu_optimized']:
            logger.info("🖥️ 選擇 CPU 優化解碼")
            return 'cpu_optimized'
        
        logger.info("📱 使用軟體解碼")
        return 'software'
    
    def _test_decoder(self, decoder_type):
        """測試解碼器是否正常工作"""
        try:
            logger.info(f"🧪 測試 {decoder_type} 解碼器...")
            
            if decoder_type == 'nvidia_cuvid':
                return self._test_nvidia_decoder()
            elif decoder_type == 'intel_qsv':
                return self._test_intel_decoder()
            elif decoder_type == 'amd_amf':
                return self._test_amd_decoder()
            elif decoder_type == 'cpu_optimized':
                return self._test_cpu_decoder()
            
            return False
            
        except Exception as e:
            logger.warning(f"❌ {decoder_type} 解碼器測試失敗: {e}")
            return False
    
    def _test_nvidia_decoder(self):
        """測試 NVIDIA 硬體解碼器"""
        try:
            # 嘗試使用 CUDA 後端
            test_cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
            test_cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
            test_cap.set(cv2.CAP_PROP_HW_DEVICE, 0)  # 使用第一個 GPU
            
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                test_cap.release()
                if ret and frame is not None:
                    logger.info("   ✅ NVIDIA 硬體解碼測試成功")
                    return True
            
            test_cap.release()
            return False
            
        except Exception as e:
            logger.warning(f"   ❌ NVIDIA 硬體解碼測試失敗: {e}")
            return False
    
    def _test_intel_decoder(self):
        """測試 Intel Quick Sync 硬體解碼器"""
        try:
            # 嘗試使用 Intel QSV
            test_cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
            test_cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
            
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                test_cap.release()
                if ret and frame is not None:
                    logger.info("   ✅ Intel QSV 硬體解碼測試成功")
                    return True
            
            test_cap.release()
            return False
            
        except Exception as e:
            logger.warning(f"   ❌ Intel QSV 硬體解碼測試失敗: {e}")
            return False
    
    def _test_amd_decoder(self):
        """測試 AMD 硬體解碼器"""
        try:
            # AMD AMF 測試
            test_cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
            test_cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
            
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                test_cap.release()
                if ret and frame is not None:
                    logger.info("   ✅ AMD AMF 硬體解碼測試成功")
                    return True
            
            test_cap.release()
            return False
            
        except Exception as e:
            logger.warning(f"   ❌ AMD AMF 硬體解碼測試失敗: {e}")
            return False
    
    def _test_cpu_decoder(self):
        """測試 CPU 優化解碼器"""
        try:
            # CPU 優化設定
            test_cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
            test_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            test_cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 3000)
            test_cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)
            
            if test_cap.isOpened():
                ret, frame = test_cap.read()
                test_cap.release()
                if ret and frame is not None:
                    logger.info("   ✅ CPU 優化解碼測試成功")
                    return True
            
            test_cap.release()
            return False
            
        except Exception as e:
            logger.warning(f"   ❌ CPU 優化解碼測試失敗: {e}")
            return False
    
    def create_capture(self):
        """創建優化的視頻捕獲對象"""
        try:
            if self.decoder_type == 'nvidia_cuvid':
                return self._create_nvidia_capture()
            elif self.decoder_type == 'intel_qsv':
                return self._create_intel_capture()
            elif self.decoder_type == 'amd_amf':
                return self._create_amd_capture()
            elif self.decoder_type == 'cpu_optimized':
                return self._create_cpu_capture()
            else:
                return self._create_software_capture()
                
        except Exception as e:
            logger.error(f"❌ 創建視頻捕獲失敗: {e}")
            logger.info("🔄 回退到軟體解碼")
            return self._create_software_capture()
    
    def _create_nvidia_capture(self):
        """創建 NVIDIA 硬體加速捕獲"""
        logger.info("🚀 創建 NVIDIA 硬體解碼捕獲...")
        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        # 設定 NVIDIA 硬體加速
        cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        cap.set(cv2.CAP_PROP_HW_DEVICE, 0)  # 使用第一個 GPU
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        
        # 嘗試設定硬體解碼格式
        try:
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
        except:
            pass
        
        return cap
    
    def _create_intel_capture(self):
        """創建 Intel Quick Sync 硬體加速捕獲"""
        logger.info("🚀 創建 Intel QSV 硬體解碼捕獲...")
        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        # 設定 Intel QSV 硬體加速
        cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        
        return cap
    
    def _create_amd_capture(self):
        """創建 AMD AMF 硬體加速捕獲"""
        logger.info("🚀 創建 AMD AMF 硬體解碼捕獲...")
        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        # 設定 AMD AMF 硬體加速
        cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        
        return cap
    
    def _create_cpu_capture(self):
        """創建 CPU 優化捕獲"""
        logger.info("🖥️ 創建 CPU 優化解碼捕獲...")
        cap = cv2.VideoCapture(self.rtsp_url, cv2.CAP_FFMPEG)
        
        # CPU 優化設定
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小緩衝區
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 3000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
        cap.set(cv2.CAP_PROP_FPS, 25)  # 限制FPS以減少延遲
        
        return cap
    
    def _create_software_capture(self):
        """創建軟體解碼捕獲"""
        logger.info("📱 創建軟體解碼捕獲...")
        cap = cv2.VideoCapture(self.rtsp_url)
        
        # 基本設定
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
        cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
        
        return cap
    
    def get_decoder_info(self):
        """獲取解碼器信息"""
        return {
            'decoder_type': self.decoder_type,
            'capabilities': self.hardware_capabilities,
            'rtsp_url': self.rtsp_url
        }
    
    def benchmark_decoder(self, test_frames=30):
        """基準測試解碼器性能"""
        logger.info(f"🏃 開始解碼器性能測試 ({test_frames} 幀)...")
        
        cap = self.create_capture()
        if not cap or not cap.isOpened():
            logger.error("❌ 無法開啟視頻流進行測試")
            return None
        
        try:
            frame_times = []
            successful_frames = 0
            
            for i in range(test_frames):
                start_time = time.time()
                ret, frame = cap.read()
                end_time = time.time()
                
                if ret and frame is not None:
                    frame_times.append(end_time - start_time)
                    successful_frames += 1
                else:
                    logger.warning(f"⚠️ 第 {i+1} 幀讀取失敗")
            
            cap.release()
            
            if successful_frames > 0:
                avg_frame_time = np.mean(frame_times)
                avg_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
                
                results = {
                    'decoder_type': self.decoder_type,
                    'successful_frames': successful_frames,
                    'total_frames': test_frames,
                    'success_rate': successful_frames / test_frames * 100,
                    'avg_frame_time_ms': avg_frame_time * 1000,
                    'avg_fps': avg_fps,
                    'min_frame_time_ms': min(frame_times) * 1000,
                    'max_frame_time_ms': max(frame_times) * 1000
                }
                
                logger.info(f"📊 解碼器性能測試結果:")
                logger.info(f"   解碼器類型: {results['decoder_type']}")
                logger.info(f"   成功率: {results['success_rate']:.1f}%")
                logger.info(f"   平均FPS: {results['avg_fps']:.1f}")
                logger.info(f"   平均幀時間: {results['avg_frame_time_ms']:.1f}ms")
                logger.info(f"   最小幀時間: {results['min_frame_time_ms']:.1f}ms")
                logger.info(f"   最大幀時間: {results['max_frame_time_ms']:.1f}ms")
                
                return results
            else:
                logger.error("❌ 沒有成功讀取任何幀")
                return None
                
        except Exception as e:
            logger.error(f"❌ 性能測試過程中出錯: {e}")
            cap.release()
            return None

def test_hardware_decoder():
    """測試硬體解碼器功能"""
    logger.info("🧪 開始硬體解碼器測試...")
    
    # 測試用的 RTSP URL（請替換為實際的 URL）
    test_rtsp_url = "rtsp://root:Abc_123@111.70.10.205:7040/axis-media/media.amp?resolution=1280x800"
    
    # 創建硬體解碼器
    decoder = HardwareVideoDecoder(test_rtsp_url, auto_detect=True)
    
    # 顯示解碼器信息
    info = decoder.get_decoder_info()
    logger.info(f"選擇的解碼器: {info['decoder_type']}")
    
    # 執行性能測試
    benchmark_results = decoder.benchmark_decoder(test_frames=10)
    
    if benchmark_results:
        logger.info("✅ 硬體解碼器測試完成")
        return True
    else:
        logger.error("❌ 硬體解碼器測試失敗")
        return False

if __name__ == "__main__":
    # 設定日誌
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 執行測試
    test_hardware_decoder()