#!/usr/bin/env python3
"""
多區域頭部檢測系統測試腳本
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime
import logging

# 設置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_multi_region_functionality():
    """測試多區域功能"""
    
    # 創建測試用的ROI區域數據
    test_regions = [
        {
            'id': 0,
            'name': 'Region_1',
            'points': [[100, 100], [300, 100], [300, 300], [100, 300]],
            'closed': True,
            'created_at': datetime.now().isoformat()
        },
        {
            'id': 1,
            'name': 'Region_2', 
            'points': [[400, 150], [600, 150], [600, 350], [400, 350]],
            'closed': True,
            'created_at': datetime.now().isoformat()
        },
        {
            'id': 2,
            'name': 'Region_3',
            'points': [[200, 400], [500, 400], [500, 600], [200, 600]],
            'closed': True,
            'created_at': datetime.now().isoformat()
        }
    ]
    
    # 保存測試ROI數據
    roi_file = "multi_roi_regions.json"
    test_data = {
        'regions': test_regions,
        'created_at': datetime.now().isoformat(),
        'total_regions': len(test_regions)
    }
    
    with open(roi_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    logger.info(f"已創建測試ROI文件: {roi_file}")
    logger.info(f"包含 {len(test_regions)} 個測試區域")
    
    # 創建測試圖像
    test_image = np.zeros((800, 800, 3), dtype=np.uint8)
    
    # 繪製測試區域
    colors = [(0, 255, 255), (0, 255, 0), (255, 0, 0)]
    
    for i, region in enumerate(test_regions):
        color = colors[i % len(colors)]
        points = np.array([region['points']], dtype=np.int32)
        cv2.polylines(test_image, points, isClosed=True, color=color, thickness=3)
        
        # 添加區域標籤
        label_pos = tuple(region['points'][0])
        cv2.putText(test_image, region['name'], 
                   (label_pos[0], label_pos[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
    
    # 添加一些模擬的檢測點
    detection_points = [
        (150, 200),  # Region_1 內
        (450, 250),  # Region_2 內
        (350, 500),  # Region_3 內
        (700, 100),  # 區域外
        (250, 150),  # Region_1 內
        (550, 200),  # Region_2 內
    ]
    
    for point in detection_points:
        cv2.circle(test_image, point, 8, (0, 0, 255), -1)
        cv2.putText(test_image, "HEAD", (point[0] + 10, point[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # 保存測試圖像
    test_image_path = "test_multi_region_image.jpg"
    cv2.imwrite(test_image_path, test_image)
    logger.info(f"已創建測試圖像: {test_image_path}")
    
    # 測試區域內點檢測
    logger.info("=== 測試區域內點檢測 ===")
    for i, region in enumerate(test_regions):
        roi_np = np.array(region['points'], dtype=np.int32)
        count_in_region = 0
        
        for point in detection_points:
            if cv2.pointPolygonTest(roi_np, point, False) >= 0:
                count_in_region += 1
        
        logger.info(f"{region['name']}: {count_in_region} 個檢測點")
    
    # 計算總計
    total_in_regions = 0
    for region in test_regions:
        roi_np = np.array(region['points'], dtype=np.int32)
        for point in detection_points:
            if cv2.pointPolygonTest(roi_np, point, False) >= 0:
                total_in_regions += 1
    
    logger.info(f"所有區域內總計: {total_in_regions} 個檢測點")
    logger.info(f"總檢測點數: {len(detection_points)} 個")
    
    return True

def test_statistics_functionality():
    """測試統計功能"""
    logger.info("=== 測試統計功能 ===")
    
    # 模擬統計數據
    region_statistics = {
        'Region_1': {
            'total_detections': 150,
            'max_count': 8,
            'avg_count': 3.2,
            'detection_count': 47
        },
        'Region_2': {
            'total_detections': 89,
            'max_count': 5,
            'avg_count': 2.1,
            'detection_count': 42
        },
        'Region_3': {
            'total_detections': 203,
            'max_count': 12,
            'avg_count': 4.8,
            'detection_count': 42
        }
    }
    
    total_statistics = {
        'total_count': 15,
        'region_counts': [5, 3, 7],
        'detection_history': [
            {
                'timestamp': datetime.now().isoformat(),
                'total_count': 15,
                'region_counts': {'Region_1': 5, 'Region_2': 3, 'Region_3': 7}
            }
        ]
    }
    
    # 創建測試報告
    report_data = {
        'session_info': {
            'start_time': datetime.now().isoformat(),
            'rtsp_url': 'test://localhost',
            'total_regions': 3,
            'device': 'cuda',
            'model_type': 'person'
        },
        'statistics': total_statistics,
        'region_statistics': region_statistics,
        'performance': {
            'avg_fps': 25.6,
            'avg_frame_time_ms': 39.1,
            'avg_gpu_memory_mb': 1024.5,
            'max_gpu_memory_mb': 1156.2
        }
    }
    
    # 保存測試報告
    report_file = f"test_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"已創建測試統計報告: {report_file}")
    
    # 顯示統計摘要
    logger.info("統計摘要:")
    for region_name, stats in region_statistics.items():
        logger.info(f"  {region_name}: 平均 {stats['avg_count']:.1f}, "
                   f"最大 {stats['max_count']}, "
                   f"檢測次數 {stats['detection_count']}")
    
    return True

def main():
    """主測試函數"""
    logger.info("開始多區域頭部檢測系統測試")
    
    try:
        # 測試多區域功能
        if test_multi_region_functionality():
            logger.info("✓ 多區域功能測試通過")
        
        # 測試統計功能
        if test_statistics_functionality():
            logger.info("✓ 統計功能測試通過")
        
        logger.info("=== 所有測試完成 ===")
        logger.info("現在可以運行主程序來測試完整功能:")
        logger.info("python gpu_optimized_head_detection.py --rtsp-url YOUR_RTSP_URL")
        
        # 顯示控制說明
        print("""
=== 多區域頭部檢測控制說明 ===
鼠標操作:
  左鍵點擊: 添加點到當前區域
  右鍵點擊: 完成當前區域繪製
  中鍵點擊: 選擇/取消選擇區域

鍵盤操作:
  'q': 退出程序
  'c': 清除所有ROI區域
  'n': 開始繪製新區域
  'd': 刪除選中的區域
  's': 保存當前幀
  'i': 顯示區域信息
  'h': 顯示幫助信息

區域繪製流程:
1. 按 'n' 開始新區域
2. 左鍵點擊添加點 (至少3個點)
3. 右鍵點擊完成區域
4. 重複步驟1-3添加更多區域
        """)
        
    except Exception as e:
        logger.error(f"測試過程中出錯: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
