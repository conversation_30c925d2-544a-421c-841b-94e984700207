# WebSocket 錯誤修復指南

## 問題描述

運行 `gpu_optimized_head_detection.py` 時出現以下錯誤：

```
ERROR:__main__:連接WebSocket流失敗: module 'websocket' has no attribute 'WebSocketApp'
```

## 問題原因

這個錯誤是由於安裝了錯誤的 WebSocket 包導致的：

1. **錯誤的包**: `websocket` - 這是一個簡單的 WebSocket 實現，沒有 `WebSocketApp` 類
2. **正確的包**: `websocket-client` - 包含完整的 WebSocket 功能，包括 `WebSocketApp` 類

## 解決方案

### 方法 1: 手動修復（推薦）

```bash
# 1. 卸載錯誤的 websocket 包
pip uninstall websocket -y

# 2. 安裝正確的 websocket-client 包
pip install websocket-client

# 3. 驗證安裝
python tmp_rovodev_test_websocket.py
```

### 方法 2: 使用更新的 requirements.txt

```bash
# 使用更新後的 requirements.txt（已包含 websocket-client）
pip install -r requirements.txt
```

### 方法 3: 使用修復腳本

```bash
# 運行自動修復腳本
python tmp_rovodev_websocket_fix.py
```

## 驗證修復

運行測試腳本確認修復成功：

```bash
python tmp_rovodev_test_websocket.py
```

成功的輸出應該是：
```
🧪 測試 WebSocket 導入...
✅ websocket 模組導入成功
✅ WebSocketApp 類可用
✅ WebSocketApp 實例創建成功
🎉 WebSocket 功能完全正常！
```

## 技術細節

### 代碼中的使用

在 `gpu_optimized_head_detection.py` 第 421 行：

```python
# 創建WebSocket連接
self.ws = websocket.WebSocketApp(
    self.websocket_url,
    on_message=on_message,
    on_error=on_error,
    on_close=on_close,
    on_open=on_open
)
```

### 包的區別

| 包名 | 描述 | 包含 WebSocketApp |
|------|------|------------------|
| `websocket` | 簡單的 WebSocket 實現 | ❌ 否 |
| `websocket-client` | 完整的 WebSocket 客戶端 | ✅ 是 |

## 常見問題

### Q: 為什麼會安裝錯誤的包？
A: 因為 `websocket` 和 `websocket-client` 都是有效的 PyPI 包，容易混淆。

### Q: 卸載 websocket 包會影響其他程序嗎？
A: 通常不會，因為大多數程序使用的是 `websocket-client`。

### Q: 如何確認安裝了正確的包？
A: 運行 `python -c "import websocket; print(hasattr(websocket, 'WebSocketApp'))"`，應該返回 `True`。

## 更新的 requirements.txt

已更新 `requirements.txt` 文件，添加了正確的依賴：

```txt
# WebSocket支持 - 修復 WebSocketApp 錯誤
websocket-client>=1.0.0
```

## 後續步驟

修復完成後，可以正常運行：

```bash
python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
```

## 相關文件

- `tmp_rovodev_websocket_fix.py` - 自動修復腳本
- `tmp_rovodev_test_websocket.py` - 測試驗證腳本
- `requirements.txt` - 更新的依賴文件

---

**注意**: 修復完成後，建議刪除以 `tmp_rovodev_` 開頭的臨時文件。