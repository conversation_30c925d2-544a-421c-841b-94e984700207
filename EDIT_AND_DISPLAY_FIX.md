# 編輯功能和顯示切換修復指南

## 問題描述

1. **'e' 編輯功能不見了** - 按 'e' 鍵沒有反應，編輯模式無法切換
2. **'v' 視窗整個消失不如預期** - 應該是關閉WebSocket圖片然後顯示檢測數量，而不是隱藏整個視窗

## 問題原因

1. **缺少 `_toggle_edit_mode` 方法** - 雖然鍵盤處理中有 'e' 鍵，但缺少對應的方法實現
2. **'v' 鍵功能設計錯誤** - 原本設計為隱藏整個視窗，但需求是切換顯示內容
3. **缺少檢測統計顯示功能** - 沒有實現檢測數量統計畫面

## 修復內容

### 1. 添加編輯模式功能

#### 新增 `_toggle_edit_mode()` 方法
```python
def _toggle_edit_mode(self):
    """切換編輯模式"""
    if not hasattr(self, 'edit_mode'):
        self.edit_mode = False
    
    self.edit_mode = not self.edit_mode
    status = "開啟" if self.edit_mode else "關閉"
    logger.info(f"編輯模式已{status}")
    
    if self.edit_mode:
        logger.info("編輯模式: 可以選擇和修改現有ROI區域")
        logger.info("使用數字鍵 1-9 選擇區域進行編輯")
    else:
        logger.info("編輯模式已關閉，回到正常檢測模式")
```

#### 添加編輯模式屬性
```python
self.edit_mode = False  # 編輯模式狀態
```

### 2. 重新設計 'v' 鍵功能

#### 新增 `_toggle_websocket_display()` 方法
```python
def _toggle_websocket_display(self):
    """切換WebSocket圖片顯示，關閉時顯示檢測數量"""
    if not hasattr(self, 'show_websocket_image'):
        self.show_websocket_image = True
    
    self.show_websocket_image = not self.show_websocket_image
    status = "顯示WebSocket圖片" if self.show_websocket_image else "顯示檢測數量"
    logger.info(f"切換到: {status}")
    
    if not self.show_websocket_image:
        # 顯示當前檢測統計
        self._show_detection_stats()
```

#### 修改鍵盤處理
```python
elif key == ord('v'):
    self._toggle_websocket_display()  # 改為新的功能
```

### 3. 添加檢測統計功能

#### 新增 `_show_detection_stats()` 方法
- 計算ROI區域檢測數量
- 計算公交車區域檢測數量
- 顯示詳細統計信息

#### 新增 `_create_stats_frame()` 方法
- 創建檢測統計顯示畫面
- 顯示各區域檢測數量
- 包含時間戳和操作提示

#### 新增 `_count_detections_in_region()` 方法
- 計算指定多邊形區域內的檢測數量
- 使用檢測框中心點判斷是否在區域內

### 4. 修改顯示邏輯

#### 更新顯示處理代碼
```python
if self.display_enabled and self.window_created:
    try:
        if self.show_websocket_image:
            # 顯示WebSocket圖片
            cv2.imshow(self.window_name, annotated_frame)
        else:
            # 顯示檢測數量統計畫面
            stats_frame = self._create_stats_frame(annotated_frame.shape, detection_results)
            cv2.imshow(self.window_name, stats_frame)
```

#### 添加顯示控制屬性
```python
self.show_websocket_image = True  # 是否顯示WebSocket圖片
```

### 5. 更新幫助信息

```
基本操作:
  q     - 退出程序
  v     - 切換WebSocket圖片/檢測統計顯示  # 更新說明
  d     - 切換顯示模式 (顯示/隱藏視頻窗口)
  h     - 顯示此幫助信息

編輯模式:
  e     - 切換編輯模式                    # 確保功能可用
  1-9   - 選擇區域進行編輯 (編輯模式下)
```

## 功能說明

### 'e' 鍵 - 編輯模式切換
- **功能**: 切換ROI區域編輯模式
- **開啟時**: 可以選擇和修改現有ROI區域
- **關閉時**: 回到正常檢測模式
- **配合使用**: 數字鍵 1-9 選擇要編輯的區域

### 'v' 鍵 - 顯示內容切換
- **模式1**: 顯示WebSocket圖片（默認）
  - 顯示實時視頻流和檢測結果
  - 包含檢測框和ROI區域標註
  
- **模式2**: 顯示檢測統計
  - 黑色背景的統計信息畫面
  - 顯示各區域檢測數量
  - 包含操作提示和時間戳

### 檢測統計畫面內容
- **總檢測數量**: 當前幀的總檢測數
- **ROI區域統計**: ROI區域數量和檢測數量
- **公交車區域統計**: 公交車區域數量和檢測數量
- **操作提示**: 如何切換回WebSocket圖片
- **時間戳**: 統計更新時間

## 驗證修復

### 測試腳本
```bash
python tmp_rovodev_test_fixed_features.py
```

### 手動測試步驟

1. **測試編輯模式**:
   ```bash
   python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
   ```
   - 按 'e' 鍵，應該看到編輯模式切換信息
   - 再按 'e' 鍵，應該看到編輯模式關閉信息

2. **測試顯示切換**:
   - 按 'v' 鍵，應該從WebSocket圖片切換到檢測統計畫面
   - 再按 'v' 鍵，應該切換回WebSocket圖片
   - 統計畫面應該顯示檢測數量和區域信息

3. **測試幫助信息**:
   - 按 'h' 鍵，檢查幫助信息是否正確更新

### 預期結果

#### 'e' 鍵測試
```
編輯模式已開啟
編輯模式: 可以選擇和修改現有ROI區域
使用數字鍵 1-9 選擇區域進行編輯
```

#### 'v' 鍵測試
```
切換到: 顯示檢測數量
=== 檢測統計信息 ===
ROI區域檢測數量: X
公交車區域檢測數量: Y
總檢測數量: Z
...
```

## 技術實現細節

### 狀態管理
- `edit_mode`: 編輯模式狀態
- `show_websocket_image`: 顯示模式狀態
- 兩個狀態獨立管理，互不影響

### 檢測統計算法
1. 遍歷所有檢測結果
2. 計算每個檢測框的中心點
3. 使用 `cv2.pointPolygonTest` 判斷點是否在多邊形內
4. 統計各區域內的檢測數量

### 畫面渲染
- 使用 OpenCV 在黑色背景上繪製文字
- 自適應文字大小和位置
- 包含錯誤處理機制

## 相關文件

- `gpu_optimized_head_detection.py` - 主程序（已修復）
- `tmp_rovodev_test_fixed_features.py` - 功能測試腳本
- `EDIT_AND_DISPLAY_FIX.md` - 本修復文檔

---

**狀態**: ✅ 已修復  
**測試**: ✅ 需要驗證  
**功能**: 🎯 'e' 編輯功能和 'v' 顯示切換功能已實現