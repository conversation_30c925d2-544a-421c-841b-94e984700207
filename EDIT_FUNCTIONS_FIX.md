# 編輯功能修復指南

## 問題描述

1. **按 'e' 沒反應** - 編輯模式無法切換
2. **滑鼠在選取區線條上點兩下沒反應** - 雙擊編輯功能無效
3. **在轉折點上點兩下沒反應** - 點編輯功能無效

## 問題原因

主要的 `gpu_optimized_head_detection.py` 文件缺少完整的編輯功能實現：

1. **缺少鼠標回調函數** - `_mouse_callback` 方法不完整
2. **缺少區域選擇方法** - `_select_region_for_edit` 等方法缺失
3. **缺少區域添加方法** - `_add_new_region` 方法缺失
4. **缺少編輯相關屬性** - 編輯狀態管理屬性未初始化

## 修復內容

### 1. 完整的鼠標回調函數

#### 新增完整的 `_mouse_callback()` 方法
```python
def _mouse_callback(self, event, x, y, flags, param):
    """鼠標回調函數用於繪製和編輯ROI區域"""
    if event == cv2.EVENT_LBUTTONDOWN:
        # 左鍵點擊：編輯模式處理或添加點
        if self.edit_mode and self.selected_region_index >= 0:
            self._handle_edit_mode_click(x, y)
            return
        # 添加ROI點
        if not self.current_region_closed:
            self.current_region_points.append([x, y])
            
    elif event == cv2.EVENT_RBUTTONDOWN:
        # 右鍵點擊：完成區域或退出編輯
        if self.edit_mode:
            self._exit_edit_mode()
            return
        if len(self.current_region_points) > 2:
            self._add_new_region()
            
    elif event == cv2.EVENT_MBUTTONDOWN:
        # 中鍵點擊：選擇區域
        self._select_region_at_point(x, y)
        
    elif event == cv2.EVENT_LBUTTONDBLCLK:
        # 雙擊左鍵：進入編輯模式
        self._enter_edit_mode_at_point(x, y)
```

### 2. 區域選擇和編輯功能

#### 新增 `_select_region_at_point()` 方法
- 支持點擊區域內部或邊界選擇
- 智能距離計算和候選區域排序
- 支持人頭區域和公交車區域選擇

#### 新增 `_select_region_for_edit()` 方法
- 數字鍵 1-9 快速選擇區域
- 自動區分人頭區域和公交車區域
- 提供詳細的選擇反饋

#### 新增 `_enter_edit_mode_at_point()` 方法
- 雙擊進入編輯模式
- 自動選擇點擊位置的區域
- 智能模式切換

### 3. 區域管理功能

#### 新增 `_add_new_region()` 方法
```python
def _add_new_region(self):
    """添加新的ROI區域到對應的列表中"""
    if len(self.current_region_points) < 3:
        return -1
    
    new_region = {
        'points': self.current_region_points.copy(),
        'polygon': self.current_region_points.copy(),
        'name': f'{self.current_region_type.title()}_Region_{len(regions) + 1}',
        'type': self.current_region_type
    }
    
    # 根據類型添加到對應列表
    if self.current_region_type == "person":
        self.roi_regions.append(new_region)
    elif self.current_region_type == "bus":
        self.bus_regions.append(new_region)
    
    # 自動保存
    self._save_multi_roi()
    return region_id
```

### 4. 編輯狀態管理

#### 新增編輯相關屬性
```python
# 編輯模式相關屬性
self.edit_point_index = -1  # 當前編輯的點索引
self.drag_mode = False  # 拖拽模式
self.edit_threshold = 15  # 編輯點選擇閾值
```

#### 編輯模式方法
- `_exit_edit_mode()` - 退出編輯模式
- `_handle_edit_mode_click()` - 處理編輯模式點擊

## 功能說明

### 'e' 鍵 - 編輯模式切換
- **功能**: 切換進入/退出編輯模式
- **狀態提示**: 控制台顯示編輯模式狀態
- **配合使用**: 數字鍵 1-9 快速選擇區域

### 鼠標編輯操作

#### 左鍵單擊
- **非編輯模式**: 添加ROI點到當前區域
- **編輯模式**: 處理編輯操作（移動點等）

#### 右鍵單擊
- **非編輯模式**: 完成當前ROI區域繪製
- **編輯模式**: 退出編輯模式

#### 中鍵單擊
- **功能**: 選擇指定位置的ROI區域
- **智能選擇**: 優先選擇距離最近的區域
- **容差範圍**: 20像素選擇容差

#### 左鍵雙擊
- **功能**: 在指定點進入編輯模式
- **自動選擇**: 自動選擇雙擊位置的區域
- **快速編輯**: 一步進入編輯狀態

### 數字鍵 1-9
- **功能**: 快速選擇區域進行編輯
- **順序**: 先人頭區域，後公交車區域
- **反饋**: 顯示選中的區域信息

## 使用流程

### 創建新區域
1. 按 'n' 開始新人頭區域或 'b' 開始新公交車區域
2. 左鍵點擊添加多個點
3. 右鍵點擊完成區域（自動保存）

### 編輯現有區域
1. **方法1**: 按 'e' 進入編輯模式，然後用數字鍵選擇區域
2. **方法2**: 中鍵點擊區域選擇，然後按 'e' 進入編輯模式
3. **方法3**: 直接雙擊區域線條或轉折點進入編輯模式

### 退出編輯
- 按 'e' 切換退出編輯模式
- 右鍵點擊退出編輯模式

## 故障排除

### 'e' 鍵沒反應
1. **檢查窗口焦點**: 確保視頻窗口處於活動狀態
2. **檢查控制台**: 查看是否有錯誤信息
3. **檢查方法存在**: 確認 `_toggle_edit_mode` 方法已添加

### 雙擊沒反應
1. **檢查區域存在**: 確保已有ROI區域
2. **點擊位置**: 確保點擊在區域線條或轉折點附近
3. **嘗試中鍵**: 使用中鍵點擊選擇區域
4. **檢查容差**: 確保點擊在20像素容差範圍內

### 區域選擇問題
1. **使用數字鍵**: 嘗試用 1-9 數字鍵快速選擇
2. **檢查區域數量**: 確認區域索引在有效範圍內
3. **查看控制台**: 檢查選擇反饋信息

## 驗證修復

### 測試腳本
```bash
python tmp_rovodev_test_edit_functions.py
```

### 手動測試步驟

1. **測試編輯模式切換**:
   ```bash
   python gpu_optimized_head_detection.py --websocket-url ws://192.168.1.102:8004
   ```
   - 按 'e' 鍵，檢查控制台是否顯示編輯模式切換信息

2. **測試區域選擇**:
   - 先創建一些ROI區域（按 'n' 或 'b'）
   - 按數字鍵 1-9 測試快速選擇
   - 中鍵點擊區域測試點選擇

3. **測試雙擊編輯**:
   - 在ROI區域線條上雙擊左鍵
   - 檢查是否進入編輯模式並選中區域

### 預期結果

#### 'e' 鍵測試
```
編輯模式已開啟
編輯模式: 可以選擇和修改現有ROI區域
使用數字鍵 1-9 選擇區域進行編輯
```

#### 雙擊測試
```
✅ 選中person區域 1: Person_Region_1 (邊界附近 5.2px)
進入編輯模式 - person區域 1
```

#### 數字鍵測試
```
✅ 快速選中人頭區域 1: Person_Region_1
```

## 技術實現細節

### 區域選擇算法
1. 收集所有候選區域（人頭+公交車）
2. 計算點到區域的距離（內部距離為0）
3. 按距離排序，選擇最近的區域
4. 提供詳細的選擇反饋

### 編輯狀態管理
- `edit_mode`: 編輯模式開關
- `selected_region_index`: 當前選中的區域索引
- `selected_region_type`: 當前選中的區域類型
- `edit_point_index`: 當前編輯的點索引

### 鼠標事件處理
- 事件類型判斷和分發
- 編輯模式和普通模式的不同處理
- 智能的區域選擇和編輯邏輯

## 相關文件

- `gpu_optimized_head_detection.py` - 主程序（已修復）
- `tmp_rovodev_test_edit_functions.py` - 編輯功能測試腳本
- `EDIT_FUNCTIONS_FIX.md` - 本修復文檔

---

**狀態**: ✅ 已修復  
**測試**: ✅ 需要驗證  
**功能**: 🎯 完整的編輯功能已實現