# 多區域頭部檢測系統

## 概述

這是一個增強版的GPU優化YOLOv5頭部檢測系統，支持多個選擇區域（ROI）的同時檢測和統計。

## 新增功能

### 1. 多區域選擇支持
- 支持繪製和管理多個獨立的ROI區域
- 每個區域可以有不同的形狀和大小
- 自動為每個區域分配唯一的ID和名稱

### 2. 聚合統計功能
- 實時顯示每個區域的檢測人數
- 計算所有區域的總人數
- 提供詳細的統計歷史記錄

### 3. 持久化存儲
- 自動保存繪製的ROI區域到JSON文件
- 支持程序重啟後自動加載之前的區域設置
- 生成詳細的檢測統計報告

### 4. 增強的用戶界面
- 直觀的多色區域顯示
- 實時的區域選擇和編輯功能
- 豐富的鍵盤快捷鍵支持

## 使用方法

### 基本運行
```bash
python gpu_optimized_head_detection.py --rtsp-url YOUR_RTSP_URL
```

### 測試功能
```bash
python test_multi_region.py
```

## 操作指南

### 鼠標操作
- **左鍵點擊**: 添加點到當前正在繪製的區域
- **右鍵點擊**: 完成當前區域的繪製並保存
- **中鍵點擊**: 選擇或取消選擇已存在的區域（用於編輯）

### 鍵盤快捷鍵
- **'q'**: 退出程序
- **'c'**: 清除所有ROI區域
- **'n'**: 開始繪製新的區域
- **'d'**: 刪除當前選中的區域
- **'s'**: 保存當前幀到圖片文件
- **'i'**: 顯示詳細的區域信息和統計數據
- **'h'**: 顯示幫助信息

### 區域繪製流程
1. 按 **'n'** 開始繪製新區域
2. 用左鍵點擊添加至少3個點來定義區域邊界
3. 右鍵點擊完成區域繪製
4. 重複上述步驟添加更多區域

## 文件說明

### 配置文件
- `multi_roi_regions.json`: 存儲所有ROI區域的配置信息
- `detection_report_YYYYMMDD_HHMMSS.json`: 檢測會話的統計報告

### 輸出文件
- `gpu_head_frame_XXXX.jpg`: 保存的檢測幀圖片
- `gpu_head_detection.mp4`: 可選的輸出視頻文件

## 統計功能

### 實時統計
- 每個區域的當前人數
- 所有區域的總人數
- GPU內存使用情況
- 檢測FPS性能

### 歷史統計
- 每個區域的平均人數
- 每個區域的最大人數
- 總檢測次數
- 累計檢測人數

### 統計報告
程序結束時會自動生成包含以下信息的JSON報告：
- 會話基本信息（開始時間、RTSP地址、設備信息等）
- ROI區域配置
- 詳細統計數據
- 性能指標

## 技術特性

### GPU優化
- 支持CUDA加速
- FP16半精度推理
- 批處理優化
- GPU內存管理

### 多區域處理
- 高效的多邊形點包含測試
- 並行區域檢測處理
- 智能的檢測結果分配

### 數據持久化
- JSON格式的配置存儲
- 兼容舊版本ROI格式
- 自動備份和恢復

## 系統要求

- Python 3.7+
- PyTorch with CUDA support
- OpenCV 4.x
- NumPy
- 支持CUDA的GPU（推薦）

## 故障排除

### 常見問題
1. **ROI區域無法保存**: 確保程序有寫入當前目錄的權限
2. **GPU內存不足**: 降低batch_size參數或使用CPU模式
3. **RTSP連接失敗**: 檢查網絡連接和RTSP URL格式

### 調試模式
設置環境變量啟用詳細日志：
```bash
export PYTHONPATH=.
python gpu_optimized_head_detection.py --rtsp-url YOUR_URL
```

## 更新日志

### v2.0 - 多區域支持
- 添加多區域ROI支持
- 實現聚合統計功能
- 增強用戶界面
- 添加統計報告生成

### v1.0 - 基礎版本
- 單區域ROI支持
- GPU優化檢測
- 基本統計功能

## 許可證

本項目遵循原項目的許可證條款。

## 貢獻

歡迎提交問題報告和功能請求。請確保在提交前測試您的更改。
