# ROI 區域選取邊界問題修正說明

## 問題分析

在分析 `gpu_optimized_head_detection.py` 代碼後，發現了以下選取區域邊界的問題：

### 1. 重複函數定義
- **問題**：存在兩個相同名稱的 `_toggle_display` 函數（第813行和第834行）
- **修正**：移除重複的函數定義，保留功能更完整的版本

### 2. 區域邊界檢測不夠精確
- **問題**：`_select_region_at_point` 函數的邊界檢測邏輯過於簡單
- **修正**：
  - 增加選擇容差從20像素到25像素
  - 添加額外的邊線距離檢測算法
  - 實現 `_get_distance_to_polygon_edge` 函數計算點到多邊形邊線的精確距離
  - 實現 `_point_to_line_distance` 函數計算點到線段的最短距離

### 3. 編輯模式功能不完整
- **問題**：`_handle_edit_mode_click` 函數只有基本的日誌記錄
- **修正**：
  - 實現完整的頂點編輯功能
  - 支持移動現有頂點
  - 支持在邊線上插入新頂點
  - 添加 `_find_edge_insertion_point` 函數找到最適合插入頂點的位置

### 4. 缺少視覺反饋
- **問題**：選中的區域沒有明顯的視覺標識
- **修正**：
  - 選中的人頭區域顯示為黃色邊框
  - 選中的公車區域顯示為青色邊框
  - 選中區域顯示白色外框高亮
  - 顯示區域頂點和 "[選中]" 標記
  - 正在創建的區域顯示點序號

## 新增功能

### 1. 改進的鼠標操作
- **中鍵點擊**：選擇區域（支持內部和邊界附近點擊）
- **雙擊左鍵**：快速進入編輯模式
- **編輯模式左鍵**：移動頂點或插入新頂點
- **編輯模式右鍵**：刪除最接近的頂點（至少保留3個頂點）

### 2. 增強的區域選擇算法
```python
def _select_region_at_point(self, x, y):
    # 1. 檢查點是否在區域內部（優先級最高）
    # 2. 檢查點是否在區域邊界附近
    # 3. 額外檢查點到邊線的精確距離
    # 4. 按距離排序選擇最佳匹配
```

### 3. 精確的邊界距離計算
```python
def _get_distance_to_polygon_edge(self, x, y, polygon_points):
    # 計算點到多邊形每條邊線的最短距離
    # 返回最小距離值
```

### 4. 完整的編輯功能
- 頂點移動：點擊現有頂點並拖拽
- 頂點插入：點擊邊線插入新頂點
- 頂點刪除：右鍵點擊頂點刪除（保留至少3個頂點）

## 使用說明

### 基本操作
1. **創建區域**：
   - 按 'n' 開始新人頭區域
   - 按 'b' 開始新公車區域
   - 左鍵添加頂點，右鍵完成區域

2. **選擇區域**：
   - 中鍵點擊區域內部或邊界附近
   - 使用數字鍵 1-9 快速選擇

3. **編輯區域**：
   - 按 'e' 切換編輯模式
   - 左鍵移動頂點或插入新頂點
   - 右鍵刪除頂點

### 視覺標識
- **人頭區域**：紅色邊框，選中時變為黃色
- **公車區域**：藍色邊框，選中時變為青色
- **選中區域**：顯示白色外框和頂點標記
- **正在創建**：顯示點序號和連接線

## 技術改進

### 1. 邊界檢測算法
- 使用 `cv2.pointPolygonTest` 進行基本檢測
- 實現自定義邊線距離計算
- 支持多候選區域的智能選擇

### 2. 錯誤處理
- 添加區域索引範圍檢查
- 防止刪除過多頂點（至少保留3個）
- 改進錯誤日誌信息

### 3. 用戶體驗
- 增加操作提示信息
- 改進幫助文檔
- 添加視覺反饋

## 測試建議

1. **邊界選擇測試**：
   - 測試點擊區域內部的選擇
   - 測試點擊邊界附近的選擇
   - 測試重疊區域的選擇優先級

2. **編輯功能測試**：
   - 測試頂點移動功能
   - 測試邊線插入功能
   - 測試頂點刪除功能

3. **視覺反饋測試**：
   - 確認選中區域的高亮顯示
   - 確認頂點標記的顯示
   - 確認區域標籤的更新

這些修正大大改善了ROI區域選取的精確度和用戶體驗，使得區域編輯更加直觀和高效。
