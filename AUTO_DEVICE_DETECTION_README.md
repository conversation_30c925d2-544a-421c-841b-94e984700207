# GPU與硬體加速功能說明

## 修改摘要

已成功修改 `gpu_optimized_head_detection.py`，實現以下功能：
1. 自動偵測並切換CPU與GPU的功能（以GPU優先）
2. **新增功能：自動偵測並啟用CPU與GPU的硬體解碼加速**

## 主要功能

### 1. 自動設備偵測
- **智能選擇**: 程式會自動偵測最佳可用設備
- **GPU優先**: 優先使用GPU，如果GPU不可用或性能不佳則自動切換到CPU
- **性能測試**: 會測試GPU性能確保適合實時處理

### 1.1 硬體解碼加速 ⭐新功能⭐
- **自動偵測**: 自動檢測並啟用最佳硬體解碼器
- **多平台支援**: 支援NVIDIA、Intel和AMD的硬體解碼加速
- **性能測試**: 啟動時會測試解碼器性能確保穩定運行
- **智能回退**: 如果硬體解碼失敗，會自動回退到標準解碼

### 2. 設備選項
```bash
--device auto    # 自動偵測（預設）
--device cuda    # 強制使用GPU
--device cpu     # 強制使用CPU
```

### 2.1 硬體解碼選項 ⭐新功能⭐
```bash
--use-hardware-decoding      # 啟用硬體解碼（預設）
--disable-hardware-decoding  # 停用硬體解碼，強制使用軟體解碼
```

### 3. 運行時切換
- 支援程式運行時動態切換CPU/GPU
- 自動處理模型重新載入和優化設置
- 智能記憶體管理

## 修改的主要方法

### 新增方法：
1. `_auto_detect_device()` - 自動偵測最佳設備
2. `_detect_best_device()` - 智能設備選擇邏輯
3. `_test_gpu_performance()` - GPU性能測試
4. `_setup_cpu_optimization()` - CPU優化設置
5. `_log_device_info()` - 設備信息記錄
6. `switch_device()` - 運行時設備切換

### 硬體解碼新增方法： ⭐新功能⭐
1. `HardwareVideoDecoder` 類 - 硬體解碼器自動偵測與管理
2. `_detect_hardware_capabilities()` - 偵測硬體解碼能力
3. `_auto_select_decoder()` - 自動選擇最佳解碼器
4. `_test_decoder()` - 測試解碼器性能
5. `create_capture()` - 創建優化的視頻捕獲對象
6. `benchmark_decoder()` - 解碼器性能測試

### 修改的方法：
1. `__init__()` - 支援auto模式
2. `_setup_gpu_optimization()` - 增強錯誤處理和自動切換
3. `main()` - 更新命令行參數和檢查邏輯

### 硬體解碼修改的方法： ⭐新功能⭐
1. `connect_stream()` - 支援硬體解碼
2. `cleanup()` - 清理硬體解碼資源
3. `GPUOptimizedRTSPProcessor.__init__()` - 增加硬體解碼選項

## 使用範例

### 基本使用（自動模式）
```bash
python gpu_optimized_head_detection.py --rtsp-url "your_rtsp_url"
```

### 強制使用特定設備
```bash
# 強制GPU
python gpu_optimized_head_detection.py --device cuda --rtsp-url "your_rtsp_url"

# 強制CPU
python gpu_optimized_head_detection.py --device cpu --rtsp-url "your_rtsp_url"
```

### 硬體解碼控制 ⭐新功能⭐
```bash
# 啟用硬體解碼（預設）
python gpu_optimized_head_detection.py --use-hardware-decoding --rtsp-url "your_rtsp_url"

# 停用硬體解碼
python gpu_optimized_head_detection.py --disable-hardware-decoding --rtsp-url "your_rtsp_url"

# 完整控制（停用硬體編碼和解碼）
python gpu_optimized_head_detection.py --disable-hardware-encoding --disable-hardware-decoding --rtsp-url "your_rtsp_url"
```

## 自動偵測邏輯

### GPU/CPU 自動偵測
1. **檢查用戶指定**: 如果用戶指定cpu或cuda，則遵循用戶選擇
2. **CUDA可用性**: 檢查CUDA是否安裝和可用
3. **GPU記憶體**: 確保GPU有足夠記憶體（至少2GB）
4. **性能測試**: 執行基本運算測試確保GPU性能
5. **自動切換**: 如果GPU測試失敗，自動切換到CPU

### 硬體解碼自動偵測 ⭐新功能⭐
1. **硬體能力檢測**: 檢測系統支援的硬體解碼器
2. **優先順序選擇**: NVIDIA > Intel > AMD > CPU優化 > 軟體解碼
3. **性能測試**: 測試選定解碼器的性能和穩定性
4. **智能回退**: 如果硬體解碼失敗，自動回退到標準解碼

## 智能優化

### GPU/CPU 優化
- **記憶體管理**: 根據GPU記憶體自動調整批次大小
- **線程優化**: CPU模式下限制線程數避免過度佔用
- **錯誤恢復**: 設備切換失敗時自動恢復到原設備
- **詳細日誌**: 提供詳細的設備選擇和切換信息

### 硬體解碼優化 ⭐新功能⭐
- **多平台支援**: 支援NVIDIA、Intel和AMD的硬體解碼
- **自動配置**: 根據檢測到的硬體自動配置最佳參數
- **性能測試**: 啟動時測試解碼器性能確保穩定運行
- **智能回退**: 硬體解碼失敗時自動切換到標準解碼

## 日誌輸出範例

### GPU/CPU 自動偵測
```
🔍 自動偵測最佳運行設備...
🧪 測試GPU性能...
   GPU基本運算測試: 0.045秒
   ✅ GPU性能測試通過
✅ 自動選擇GPU: NVIDIA GeForce RTX 3080 (10.0GB)
   - GPU數量: 1
   - CUDA版本: 11.8
🚀 GPU優化設置:
  - GPU數量: 1
  - 當前GPU: 0 (NVIDIA GeForce RTX 3080)
  - GPU總記憶體: 10.0 GB
  - GPU可用記憶體: 9.2 GB
  - CUDA版本: 11.8
  - cuDNN啟用: True
  - cuDNN基準: True
  - 批次大小: 1 (高記憶體GPU)
```

### 硬體解碼自動偵測 ⭐新功能⭐
```
🔍 偵測硬體解碼能力...
✅ NVIDIA CUVID 硬體解碼可用
✅ Intel Quick Sync 硬體解碼可用
🚀 嘗試使用硬體解碼加速...
✅ 硬體解碼啟用: nvidia_cuvid
🏃 硬體解碼性能測試通過: 120.5 FPS
RTSP流连接成功: 1280x720 @ 30fps (硬體解碼 (nvidia_cuvid))
```

## 優勢

### GPU/CPU 自動偵測優勢
1. **用戶友好**: 無需手動指定設備，程式自動選擇最佳選項
2. **容錯性強**: GPU不可用時自動切換到CPU，確保程式正常運行
3. **性能優化**: 根據硬體配置自動調整參數
4. **靈活性高**: 支援運行時動態切換設備
5. **向後兼容**: 保持原有功能的同時增加新特性

### 硬體解碼優勢 ⭐新功能⭐
1. **更高效能**: 硬體解碼可顯著提升視頻處理幀率（20-50%）
2. **降低CPU負擔**: 將解碼工作轉移到專用硬體，減輕CPU負擔
3. **降低延遲**: 特別是在處理高解析度RTSP流時
4. **提高穩定性**: 減少幀丟失和卡頓現象
5. **自動適應**: 根據系統硬體自動選擇最佳解碼方式

## 注意事項

### 一般注意事項
- 首次運行時會進行GPU性能測試，可能需要幾秒鐘
- 運行時切換設備會重新載入模型，可能造成短暫中斷
- 建議在穩定環境下使用自動模式，特殊需求時可指定特定設備

### 硬體解碼注意事項 ⭐新功能⭐
- 需要安裝支援硬體加速的OpenCV和FFmpeg版本
- NVIDIA GPU需要安裝CUDA和最新驅動
- Intel CPU/GPU需要支援Quick Sync Video技術
- AMD GPU需要支援AMF技術
- 如遇問題，可使用`--disable-hardware-decoding`參數關閉硬體解碼